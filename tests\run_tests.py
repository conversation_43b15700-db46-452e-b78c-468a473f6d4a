#!/usr/bin/env python3
"""
Test runner for the enhanced AI Sourcing Agent system.
Provides different test execution modes and reporting options.
"""

import argparse
import asyncio
import os
import sys
from pathlib import Path
from typing import List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import pytest


def run_unit_tests(verbose: bool = False, coverage: bool = False) -> int:
    """Run unit tests."""
    args = ["tests/unit", "-v" if verbose else "-q"]
    
    if coverage:
        args.extend(["--cov=src", "--cov-report=html", "--cov-report=term"])
    
    return pytest.main(args)


def run_integration_tests(verbose: bool = False, browser: bool = True) -> int:
    """Run integration tests."""
    args = ["tests/integration", "-v" if verbose else "-q", "--asyncio-mode=auto"]
    
    if not browser:
        args.extend(["-m", "not browser"])
    
    return pytest.main(args)


def run_all_tests(verbose: bool = False, coverage: bool = False) -> int:
    """Run all tests."""
    args = ["tests/", "-v" if verbose else "-q", "--asyncio-mode=auto"]
    
    if coverage:
        args.extend(["--cov=src", "--cov-report=html", "--cov-report=term"])
    
    return pytest.main(args)


def run_specific_test(test_path: str, verbose: bool = False) -> int:
    """Run a specific test file or test function."""
    args = [test_path, "-v" if verbose else "-q", "--asyncio-mode=auto"]
    return pytest.main(args)


def check_system_requirements() -> bool:
    """Check if system requirements are met for testing."""
    try:
        # Check if required packages are available
        import playwright
        import pydantic
        import httpx
        
        print("✓ All required packages are available")
        return True
        
    except ImportError as e:
        print(f"✗ Missing required package: {e}")
        return False


def setup_test_environment():
    """Set up test environment variables and configuration."""
    # Set test-specific environment variables
    os.environ["AI_SOURCING_LOGGING_LEVEL"] = "DEBUG"
    os.environ["ANONYMIZED_TELEMETRY"] = "false"
    os.environ["AI_SOURCING_HEADLESS"] = "true"
    
    # Mock API keys for testing
    if not os.environ.get("GOOGLE_API_KEY"):
        os.environ["GOOGLE_API_KEY"] = "test-google-key"
    if not os.environ.get("GEMINI_API_KEY"):
        os.environ["GEMINI_API_KEY"] = "test-gemini-key"
    
    print("✓ Test environment configured")


async def validate_system_components():
    """Validate that all system components can be imported and initialized."""
    try:
        # Test core imports
        from src.agents.enhanced_agent import EnhancedAgent
        from src.browser.browser_manager import BrowserManager
        from src.dom.service import DOMService
        from src.llm.providers.gemini_provider import GeminiProvider
        from src.tools.service import Tools
        from src.config.service import ConfigurationService
        from src.observability.service import ObservabilityService
        from src.events.event_bus import EventBus
        
        print("✓ All core components can be imported")
        
        # Test basic initialization
        config_service = ConfigurationService()
        observability_service = ObservabilityService()
        event_bus = EventBus()
        
        print("✓ Core services can be initialized")
        
        # Test configuration loading
        config_data = config_service.load_config()
        assert "browser_profiles" in config_data
        assert "llm_configs" in config_data
        assert "agent_configs" in config_data
        
        print("✓ Configuration system working")
        
        # Test observability
        observability_service.record_metric("test_metric", 1.0)
        trace_id = observability_service.start_trace()
        span = observability_service.start_span("test_span")
        observability_service.finish_span(span.span_id)
        
        print("✓ Observability system working")
        
        return True
        
    except Exception as e:
        print(f"✗ System validation failed: {e}")
        return False


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Enhanced AI Sourcing Agent Test Runner")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "all", "validate", "specific"],
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage report"
    )
    
    parser.add_argument(
        "--no-browser",
        action="store_true",
        help="Skip browser-dependent tests"
    )
    
    parser.add_argument(
        "--test-path",
        type=str,
        help="Specific test file or function to run (for 'specific' test type)"
    )
    
    parser.add_argument(
        "--check-requirements",
        action="store_true",
        help="Check system requirements before running tests"
    )
    
    args = parser.parse_args()
    
    # Check requirements if requested
    if args.check_requirements:
        if not check_system_requirements():
            return 1
    
    # Set up test environment
    setup_test_environment()
    
    # Validate system components if requested
    if args.test_type == "validate":
        print("Validating system components...")
        if asyncio.run(validate_system_components()):
            print("✓ System validation passed")
            return 0
        else:
            print("✗ System validation failed")
            return 1
    
    # Run tests based on type
    if args.test_type == "unit":
        print("Running unit tests...")
        return run_unit_tests(args.verbose, args.coverage)
    
    elif args.test_type == "integration":
        print("Running integration tests...")
        return run_integration_tests(args.verbose, not args.no_browser)
    
    elif args.test_type == "all":
        print("Running all tests...")
        return run_all_tests(args.verbose, args.coverage)
    
    elif args.test_type == "specific":
        if not args.test_path:
            print("Error: --test-path is required for 'specific' test type")
            return 1
        
        print(f"Running specific test: {args.test_path}")
        return run_specific_test(args.test_path, args.verbose)
    
    else:
        print(f"Unknown test type: {args.test_type}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
