#!/usr/bin/env python3
"""
Core functionality test for the enhanced AI Sourcing Agent.
Tests core components without requiring external HTTP dependencies.
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_configuration():
    """Test configuration loading."""
    print("Testing configuration...")
    
    try:
        from src.config.service import CONFIG
        print(f"✓ CONFIG object created: {type(CONFIG)}")
        
        # Test accessing attributes
        log_level = CONFIG.BROWSER_USE_LOGGING_LEVEL
        print(f"✓ BROWSER_USE_LOGGING_LEVEL: {log_level}")
        
        telemetry = CONFIG.ANONYMIZED_TELEMETRY
        print(f"✓ ANONYMIZED_TELEMETRY: {telemetry}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_logging():
    """Test logging setup."""
    print("\nTesting logging...")
    
    try:
        from src.utils.logging_config import setup_logging
        import logging
        
        # Setup logging
        setup_logging(log_level="INFO")
        
        # Test logger creation
        logger = logging.getLogger("test")
        logger.info("Test log message")
        print("✓ Logging setup successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_exceptions():
    """Test exception classes."""
    print("\nTesting exceptions...")
    
    try:
        from src.utils.exceptions import BrowserError, LLMError, TimeoutError, DOMError
        
        # Test BrowserError
        browser_error = BrowserError(
            "Test browser error",
            long_term_memory="Long term context",
            short_term_memory="Short term context"
        )
        print("✓ BrowserError created successfully")
        
        # Test LLMError
        llm_error = LLMError("Test LLM error", provider="test", model="test-model")
        print("✓ LLMError created successfully")
        
        # Test TimeoutError
        timeout_error = TimeoutError(
            operation="test_operation",
            timeout=30.0,
            message="Test timeout"
        )
        print("✓ TimeoutError created successfully")
        
        # Test DOMError
        dom_error = DOMError("Test DOM error", element_info={"tag": "div"})
        print("✓ DOMError created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Exception test failed: {e}")
        return False

def test_basic_models():
    """Test basic Pydantic models."""
    print("\nTesting basic models...")
    
    try:
        from src.agents.enhanced_agent import AgentSettings, AgentCurrentState
        
        # Test AgentSettings
        settings = AgentSettings(
            max_steps=10,
            step_timeout=30.0,
            max_failures=3
        )
        print("✓ AgentSettings created successfully")
        
        # Test AgentCurrentState
        state = AgentCurrentState()
        print("✓ AgentCurrentState created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic models test failed: {e}")
        return False

def test_enhanced_llm_models():
    """Test enhanced LLM models without HTTP dependencies."""
    print("\nTesting enhanced LLM models...")
    
    try:
        from src.llm.enhanced_llm import ChatInvokeUsage, ChatInvokeCompletion
        
        # Test ChatInvokeUsage
        usage = ChatInvokeUsage(
            prompt_tokens=100,
            completion_tokens=50,
            total_tokens=150
        )
        print("✓ ChatInvokeUsage created successfully")
        
        # Test cost calculation
        usage.calculate_costs(0.001, 0.002)  # $0.001 per 1k prompt, $0.002 per 1k completion
        print(f"✓ Cost calculation: ${usage.total_cost:.6f}")
        
        # Test ChatInvokeCompletion
        completion = ChatInvokeCompletion(
            completion="Test completion response",
            usage=usage,
            model="test-model",
            provider="test-provider"
        )
        print("✓ ChatInvokeCompletion created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Enhanced LLM models test failed: {e}")
        return False

def test_tools_models():
    """Test tools models."""
    print("\nTesting tools models...")
    
    try:
        from src.tools.enhanced_tools import ActionResult
        
        # Test ActionResult
        result = ActionResult(
            success=True,
            action_type="test_action",
            result="Test result",
            execution_time=0.1
        )
        print("✓ ActionResult created successfully")
        
        # Test failed result
        failed_result = ActionResult(
            success=False,
            action_type="test_action",
            error="Test error",
            execution_time=0.05
        )
        print("✓ Failed ActionResult created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Tools models test failed: {e}")
        return False

async def test_async_functionality():
    """Test basic async functionality."""
    print("\nTesting async functionality...")
    
    try:
        # Test basic async operation
        await asyncio.sleep(0.1)
        print("✓ Basic async operation successful")
        
        # Test async context manager pattern
        class MockAsyncContext:
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        async with MockAsyncContext():
            pass
        print("✓ Async context manager pattern works")
        
        return True
        
    except Exception as e:
        print(f"✗ Async functionality test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("=" * 60)
    print("ENHANCED AI SOURCING AGENT - CORE FUNCTIONALITY TEST")
    print("=" * 60)
    
    start_time = time.time()
    
    tests = [
        ("Configuration Test", test_configuration),
        ("Logging Test", test_logging),
        ("Exception Test", test_exceptions),
        ("Basic Models Test", test_basic_models),
        ("Enhanced LLM Models Test", test_enhanced_llm_models),
        ("Tools Models Test", test_tools_models),
        ("Async Functionality Test", test_async_functionality),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                failed += 1
                print(f"✗ {test_name} FAILED")
                
        except Exception as e:
            failed += 1
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    execution_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed / (passed + failed)) * 100:.1f}%")
    print(f"Execution Time: {execution_time:.2f} seconds")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 ALL CORE TESTS PASSED! The enhanced system core is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
