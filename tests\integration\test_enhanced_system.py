"""
Comprehensive integration tests for the enhanced AI Sourcing Agent system.
Tests the complete system with browser-use patterns and real browser automation.
"""

import asyncio
import pytest
import tempfile
from pathlib import Path
from typing import Dict, Any

from src.agents.enhanced_agent import EnhancedAgent
from src.browser.browser_manager import <PERSON><PERSON>er<PERSON>anager
from src.dom.service import DOMService
from src.llm.providers.gemini_provider import GeminiProvider
from src.tools.service import Tools
from src.config.service import ConfigurationService, BrowserProfile, LLMConfig, AgentConfig
from src.observability.service import ObservabilityService
from src.events.event_bus import EventBus
from src.llm.messages import BaseMessage


class TestEnhancedSystemIntegration:
    """Integration tests for the complete enhanced system."""
    
    @pytest.fixture
    async def config_service(self):
        """Create a test configuration service."""
        config_service = ConfigurationService()
        
        # Create test configurations
        browser_profile = BrowserProfile(
            name="test",
            headless=True,
            timeout=10.0,
            window_width=1280,
            window_height=720
        )
        
        llm_config = LLMConfig(
            name="test",
            provider="gemini",
            model="gemini-2.0-flash-exp",
            api_key="test-key",
            temperature=0.1
        )
        
        agent_config = AgentConfig(
            name="test",
            max_steps=10,
            step_timeout=30.0,
            enable_screenshots=False
        )
        
        # Store configurations
        config_service._browser_profiles["test"] = browser_profile
        config_service._llm_configs["test"] = llm_config
        config_service._agent_configs["test"] = agent_config
        
        return config_service
    
    @pytest.fixture
    async def browser_manager(self, config_service):
        """Create a test browser manager."""
        profile = config_service.get_browser_profile("test")
        browser_manager = BrowserManager(profile)
        
        # Initialize browser
        await browser_manager.initialize()
        
        yield browser_manager
        
        # Cleanup
        await browser_manager.close()
    
    @pytest.fixture
    async def dom_service(self, browser_manager):
        """Create a test DOM service."""
        page = browser_manager.get_current_page()
        dom_service = DOMService(page)
        return dom_service
    
    @pytest.fixture
    async def llm_provider(self, config_service):
        """Create a test LLM provider."""
        llm_config = config_service.get_llm_config("test")
        
        # Mock the GeminiProvider for testing
        class MockGeminiProvider(GeminiProvider):
            async def generate_response(self, messages, **kwargs):
                # Return a mock response
                from src.llm.views import LLMResponse, ChatInvokeUsage
                
                usage = ChatInvokeUsage(
                    prompt_tokens=100,
                    completion_tokens=50,
                    total_tokens=150
                )
                
                return LLMResponse(
                    content="This is a test response from the mock LLM provider.",
                    usage=usage,
                    model_name=self.model,
                    provider="gemini"
                )
        
        provider = MockGeminiProvider(
            api_key=llm_config.api_key,
            model=llm_config.model
        )
        
        return provider
    
    @pytest.fixture
    async def tools_service(self, browser_manager, dom_service, llm_provider):
        """Create a test tools service."""
        tools = Tools(
            browser_manager=browser_manager,
            dom_service=dom_service,
            llm_provider=llm_provider
        )
        return tools
    
    @pytest.fixture
    async def enhanced_agent(self, config_service, browser_manager, llm_provider, tools_service):
        """Create a test enhanced agent."""
        agent_config = config_service.get_agent_config("test")
        
        agent = EnhancedAgent(
            task="Test task for integration testing",
            llm_provider=llm_provider,
            browser_manager=browser_manager,
            tools=tools_service,
            config=agent_config
        )
        
        return agent
    
    @pytest.mark.asyncio
    async def test_system_initialization(self, config_service, browser_manager, dom_service, llm_provider, tools_service):
        """Test that all system components initialize correctly."""
        # Test configuration service
        assert config_service is not None
        browser_profile = config_service.get_browser_profile("test")
        assert browser_profile.name == "test"
        assert browser_profile.headless is True
        
        # Test browser manager
        assert browser_manager is not None
        assert browser_manager.browser is not None
        page = browser_manager.get_current_page()
        assert page is not None
        
        # Test DOM service
        assert dom_service is not None
        assert dom_service.page is not None
        
        # Test LLM provider
        assert llm_provider is not None
        assert llm_provider.model == "gemini-2.0-flash-exp"
        
        # Test tools service
        assert tools_service is not None
        assert tools_service.registry is not None
        
        # Test that tools are registered
        available_actions = tools_service.get_available_actions()
        assert len(available_actions) > 0
    
    @pytest.mark.asyncio
    async def test_browser_navigation(self, browser_manager, dom_service):
        """Test browser navigation and DOM extraction."""
        # Navigate to a test page
        test_url = "data:text/html,<html><body><h1>Test Page</h1><button id='test-btn'>Click Me</button></body></html>"
        await browser_manager.navigate_to(test_url)
        
        # Wait for page to load
        await asyncio.sleep(1)
        
        # Extract DOM tree
        dom_nodes = await dom_service.extract_dom_tree()
        assert len(dom_nodes) > 0
        
        # Find interactive elements
        interactive_elements = await dom_service.get_interactive_elements()
        assert len(interactive_elements) > 0
        
        # Check that button is found
        button_found = False
        for element in interactive_elements.values():
            if element.tag_name == "button" and "test-btn" in element.attributes.get("id", ""):
                button_found = True
                break
        
        assert button_found, "Test button should be found in interactive elements"
    
    @pytest.mark.asyncio
    async def test_llm_integration(self, llm_provider):
        """Test LLM provider integration."""
        # Test message creation and response generation
        messages = [BaseMessage(role="user", content="Hello, this is a test message.")]
        
        response = await llm_provider.generate_response(messages)
        
        assert response is not None
        assert response.content is not None
        assert len(response.content) > 0
        assert response.model_name == "gemini-2.0-flash-exp"
        assert response.provider == "gemini"
        assert response.usage is not None
        assert response.usage.total_tokens > 0
    
    @pytest.mark.asyncio
    async def test_tools_execution(self, tools_service, browser_manager):
        """Test tools service action execution."""
        # Test navigation action
        result = await tools_service.execute_action(
            "navigate",
            {"url": "data:text/html,<html><body><h1>Tools Test</h1></body></html>"}
        )
        
        assert result.success is True
        assert result.action_type == "navigate"
        assert "Navigated to" in result.extracted_content
        
        # Wait for navigation
        await asyncio.sleep(1)
        
        # Verify navigation worked
        page = browser_manager.get_current_page()
        title = await page.title()
        assert "Tools Test" in await page.content() or title is not None
    
    @pytest.mark.asyncio
    async def test_event_system(self, browser_manager):
        """Test event-driven architecture."""
        from src.events.browser_events import NavigateToUrlEvent
        from src.events import default_event_bus
        
        # Set up event listener
        events_received = []
        
        async def event_handler(event):
            events_received.append(event)
        
        # Subscribe to navigation events
        default_event_bus.subscribe(NavigateToUrlEvent, event_handler)
        
        # Trigger navigation
        test_url = "data:text/html,<html><body><h1>Event Test</h1></body></html>"
        await browser_manager.navigate_to(test_url)
        
        # Wait for event processing
        await asyncio.sleep(0.1)
        
        # Check that event was received
        assert len(events_received) > 0
        assert isinstance(events_received[0], NavigateToUrlEvent)
        assert events_received[0].url == test_url
    
    @pytest.mark.asyncio
    async def test_observability_system(self):
        """Test observability and telemetry collection."""
        from src.observability import observability_service, observe
        
        # Test metric recording
        observability_service.record_metric("test_metric", 42.0, "count")
        
        # Test telemetry recording
        observability_service.record_telemetry(
            "test_event",
            "integration_test",
            {"test_property": "test_value"}
        )
        
        # Test tracing
        trace_id = observability_service.start_trace()
        span = observability_service.start_span("test_span", "test_operation")
        observability_service.finish_span(span.span_id)
        
        # Verify data collection
        metrics_summary = observability_service.get_metrics_summary()
        assert "test_metric_count" in metrics_summary
        
        trace_summary = observability_service.get_trace_summary()
        assert trace_summary["total_spans"] >= 1
        assert trace_summary["completed_spans"] >= 1
        
        # Test decorator
        @observe(name="test_decorated_function")
        async def test_function():
            return "test_result"
        
        result = await test_function()
        assert result == "test_result"
        
        # Check that span was created
        updated_summary = observability_service.get_trace_summary()
        assert updated_summary["total_spans"] > trace_summary["total_spans"]
    
    @pytest.mark.asyncio
    async def test_configuration_system(self, config_service):
        """Test configuration management."""
        # Test loading configurations
        config_data = config_service.load_config()
        assert "browser_profiles" in config_data
        assert "llm_configs" in config_data
        assert "agent_configs" in config_data
        
        # Test configuration validation
        is_valid = config_service.validate_configuration()
        assert is_valid is True
        
        # Test profile retrieval
        browser_profile = config_service.get_browser_profile("test")
        assert browser_profile.name == "test"
        assert browser_profile.headless is True
        
        llm_config = config_service.get_llm_config("test")
        assert llm_config.name == "test"
        assert llm_config.provider == "gemini"
        
        agent_config = config_service.get_agent_config("test")
        assert agent_config.name == "test"
        assert agent_config.max_steps == 10
    
    @pytest.mark.asyncio
    async def test_enhanced_agent_execution(self, enhanced_agent):
        """Test enhanced agent execution with browser-use patterns."""
        # Test agent initialization
        assert enhanced_agent.task == "Test task for integration testing"
        assert enhanced_agent.llm_provider is not None
        assert enhanced_agent.browser_manager is not None
        assert enhanced_agent.tools is not None
        
        # Test message manager
        assert enhanced_agent.message_manager is not None
        
        # Create a test message
        test_message = enhanced_agent.message_manager.create_user_message("Test user message")
        assert test_message.role == "user"
        assert test_message.content == "Test user message"
        
        # Test getting messages for LLM
        messages = enhanced_agent.message_manager.get_messages_for_llm()
        assert len(messages) >= 1  # Should have at least system message
        
        # Test cost tracking
        stats = enhanced_agent.message_manager.get_conversation_stats()
        assert "total_messages" in stats
        assert "total_tokens" in stats
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, enhanced_agent, browser_manager):
        """Test complete end-to-end workflow."""
        # Set up a test page with interactive elements
        test_html = """
        <html>
        <body>
            <h1>End-to-End Test Page</h1>
            <input id="test-input" type="text" placeholder="Enter text here">
            <button id="test-button" onclick="document.getElementById('result').innerText='Button clicked!'">Click Me</button>
            <div id="result"></div>
        </body>
        </html>
        """
        
        test_url = f"data:text/html,{test_html}"
        await browser_manager.navigate_to(test_url)
        await asyncio.sleep(1)
        
        # Test DOM extraction
        dom_service = DOMService(browser_manager.get_current_page())
        dom_nodes = await dom_service.extract_dom_tree()
        assert len(dom_nodes) > 0
        
        # Test element interaction
        interactive_elements = await dom_service.get_interactive_elements()
        assert len(interactive_elements) > 0
        
        # Find and click the button
        button_element = None
        for element in interactive_elements.values():
            if element.tag_name == "button" and "test-button" in element.attributes.get("id", ""):
                button_element = element
                break
        
        assert button_element is not None, "Test button should be found"
        
        # Click the button using DOM service
        click_success = await dom_service.click_element(button_element)
        assert click_success is True
        
        # Wait for click effect
        await asyncio.sleep(0.5)
        
        # Verify the click worked by checking page content
        page_content = await browser_manager.get_current_page().content()
        assert "Button clicked!" in page_content or "result" in page_content
        
        # Test observability data collection
        from src.observability import observability_service
        
        export_data = observability_service.export_data()
        assert "session_id" in export_data
        assert "metrics" in export_data
        assert "spans" in export_data
        
        # Verify system is working end-to-end
        assert enhanced_agent is not None
        assert browser_manager.browser is not None
        assert len(dom_nodes) > 0
        assert len(interactive_elements) > 0


class TestSystemRobustness:
    """Test system robustness and error handling."""
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test system error handling and recovery."""
        from src.tools.service import Tools
        from src.tools.registry import ActionResult
        
        # Test tools with invalid parameters
        tools = Tools()
        
        # Test action execution with invalid action name
        result = await tools.execute_action("invalid_action", {})
        assert result.success is False
        assert "not found" in result.error.lower()
        
        # Test action execution with invalid parameters
        result = await tools.execute_action("navigate", {"invalid_param": "value"})
        assert result.success is False
    
    @pytest.mark.asyncio
    async def test_resource_cleanup(self):
        """Test proper resource cleanup."""
        from src.browser.browser_manager import BrowserManager
        from src.config.service import BrowserProfile
        
        # Create browser manager
        profile = BrowserProfile(headless=True)
        browser_manager = BrowserManager(profile)
        
        # Initialize and use browser
        await browser_manager.initialize()
        assert browser_manager.browser is not None
        
        # Test cleanup
        await browser_manager.close()
        
        # Verify cleanup
        # Note: In a real test, we'd check that browser processes are terminated
        # For now, we just verify the method completes without error
        assert True  # Placeholder assertion
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """Test system behavior under concurrent operations."""
        from src.observability import observability_service
        
        # Test concurrent metric recording
        async def record_metrics(prefix: str, count: int):
            for i in range(count):
                observability_service.record_metric(f"{prefix}_metric_{i}", float(i))
        
        # Run concurrent metric recording
        await asyncio.gather(
            record_metrics("test1", 10),
            record_metrics("test2", 10),
            record_metrics("test3", 10)
        )
        
        # Verify all metrics were recorded
        metrics_summary = observability_service.get_metrics_summary()
        assert len(observability_service.metrics) == 30
        
        # Test concurrent span creation
        async def create_spans(prefix: str, count: int):
            spans = []
            for i in range(count):
                span = observability_service.start_span(f"{prefix}_span_{i}")
                spans.append(span)
                await asyncio.sleep(0.001)  # Small delay
                observability_service.finish_span(span.span_id)
            return spans
        
        # Run concurrent span creation
        span_results = await asyncio.gather(
            create_spans("test1", 5),
            create_spans("test2", 5),
            create_spans("test3", 5)
        )
        
        # Verify all spans were created
        total_spans = sum(len(spans) for spans in span_results)
        assert total_spans == 15
        
        trace_summary = observability_service.get_trace_summary()
        assert trace_summary["total_spans"] >= 15


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--asyncio-mode=auto"])
