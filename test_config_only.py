#!/usr/bin/env python3
"""
Test configuration loading only.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_config():
    """Test configuration loading."""
    try:
        from src.config.service import CONFIG
        print(f"CONFIG object created: {type(CONFIG)}")
        
        # Test accessing BROWSER_USE_LOGGING_LEVEL
        log_level = CONFIG.BROWSER_USE_LOGGING_LEVEL
        print(f"BROWSER_USE_LOGGING_LEVEL: {log_level}")
        
        # Test other attributes
        telemetry = CONFIG.ANONYMIZED_TELEMETRY
        print(f"ANONYMIZED_TELEMETRY: {telemetry}")
        
        return True
        
    except Exception as e:
        print(f"Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config()
    print(f"Configuration test: {'PASSED' if success else 'FAILED'}")
