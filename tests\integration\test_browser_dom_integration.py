"""
Integration tests for browser and DOM services based on browser-use patterns.
Tests browser automation, DOM extraction, accessibility, and iframe handling.
"""

import asyncio
import pytest
import time
from typing import Dict, Any, List
from unittest.mock import AsyncMock, MagicMock, patch

from src.browser.enhanced_browser import <PERSON>hancedBrowserSession, BrowserWatchdog
from src.dom.service import EnhancedDOMService, EnhancedDOMNode, DOMRect, NodeType
from src.tools.enhanced_tools import EnhancedToolsManager, ActionResult
from src.utils.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DOMError, ElementNotFoundError


@pytest.fixture
async def mock_page():
    """Create mock Playwright page."""
    page = AsyncMock()
    page.url = "https://example.com"
    page.title.return_value = "Example Domain"
    page.viewport_size.return_value = {"width": 1920, "height": 1080}
    page.evaluate.return_value = 1.0  # device pixel ratio
    page.query_selector_all.return_value = []
    page.is_visible.return_value = True
    return page


@pytest.fixture
async def mock_browser_session(mock_page):
    """Create mock browser session."""
    session = AsyncMock(spec=EnhancedBrowserSession)
    session.session_id = "test-session-456"
    session.is_initialized = True
    session.get_current_page.return_value = mock_page
    session.pages = [mock_page]
    session.stats = {
        "pages_created": 1,
        "navigation_count": 0,
        "error_count": 0,
        "total_load_time": 0.0
    }
    return session


@pytest.fixture
async def dom_service(mock_page):
    """Create DOM service with mock page."""
    return EnhancedDOMService(mock_page)


@pytest.fixture
async def tools_manager(mock_browser_session):
    """Create tools manager with mock browser session."""
    return EnhancedToolsManager(mock_browser_session)


class TestBrowserSessionIntegration:
    """Test browser session integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_session_initialization(self, mock_browser_session):
        """Test browser session initialization."""
        # Test initialization
        result = await mock_browser_session.initialize()
        
        # Should be successful with mocked components
        mock_browser_session.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_event_driven_navigation(self, mock_browser_session, mock_page):
        """Test event-driven navigation."""
        # Mock navigation
        mock_browser_session.navigate_to_url = AsyncMock(return_value={
            "success": True,
            "url": "https://example.com",
            "title": "Example Domain"
        })
        
        # Test navigation
        result = await mock_browser_session.navigate_to_url("https://example.com")
        
        assert result["success"] is True
        assert result["url"] == "https://example.com"
    
    @pytest.mark.asyncio
    async def test_watchdog_functionality(self, mock_browser_session):
        """Test browser watchdog functionality."""
        watchdog = BrowserWatchdog(mock_browser_session)
        
        # Test health check
        health_result = await watchdog._check_browser_health()
        
        # Should return boolean
        assert isinstance(health_result, bool)
    
    @pytest.mark.asyncio
    async def test_session_recovery(self, mock_browser_session):
        """Test session recovery mechanisms."""
        # Mock recovery methods
        mock_browser_session.restart_browser = AsyncMock(return_value=True)
        mock_browser_session.restart_context = AsyncMock(return_value=True)
        
        # Test browser restart
        result = await mock_browser_session.restart_browser()
        assert result is True
        
        # Test context restart
        result = await mock_browser_session.restart_context()
        assert result is True
    
    def test_session_statistics(self, mock_browser_session):
        """Test session statistics tracking."""
        stats = mock_browser_session.stats
        
        assert "pages_created" in stats
        assert "navigation_count" in stats
        assert "error_count" in stats
        assert "total_load_time" in stats


class TestDOMServiceIntegration:
    """Test DOM service integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_dom_tree_extraction(self, dom_service, mock_page):
        """Test DOM tree extraction."""
        # Mock DOM elements
        mock_elements = [
            AsyncMock(tag_name="div", text_content="Test content"),
            AsyncMock(tag_name="button", text_content="Click me"),
            AsyncMock(tag_name="input", text_content="")
        ]
        mock_page.query_selector_all.return_value = mock_elements
        
        # Extract DOM tree
        dom_tree = await dom_service.get_dom_tree()
        
        # Verify extraction
        assert isinstance(dom_tree, dict)
        mock_page.query_selector_all.assert_called()
    
    @pytest.mark.asyncio
    async def test_enhanced_dom_tree_with_accessibility(self, dom_service, mock_page):
        """Test enhanced DOM tree with accessibility information."""
        # Mock accessibility tree
        mock_page.evaluate.return_value = {
            "0": {
                "role": "button",
                "name": "Submit",
                "properties": {"disabled": False}
            }
        }
        
        # Get enhanced DOM tree
        enhanced_tree = await dom_service.get_enhanced_dom_tree(include_accessibility=True)
        
        # Verify structure
        assert "nodes" in enhanced_tree
        assert "metadata" in enhanced_tree
        assert "accessibility_tree" in enhanced_tree
        assert "performance" in enhanced_tree
    
    @pytest.mark.asyncio
    async def test_iframe_processing(self, dom_service, mock_page):
        """Test iframe processing."""
        # Mock iframes
        mock_iframe = AsyncMock()
        mock_iframe.get_attribute.return_value = "https://iframe.example.com"
        mock_iframe.content_frame.return_value = AsyncMock()
        
        mock_page.query_selector_all.return_value = [mock_iframe]
        
        # Extract iframe trees
        iframe_trees = await dom_service._extract_iframe_trees(max_depth=2)
        
        # Verify iframe processing
        assert isinstance(iframe_trees, list)
    
    @pytest.mark.asyncio
    async def test_element_interaction_detection(self, dom_service, mock_page):
        """Test interactive element detection."""
        # Mock interactive elements
        mock_button = AsyncMock()
        mock_button.tag_name = "button"
        mock_button.get_attribute.return_value = None
        mock_button.text_content.return_value = "Click me"
        mock_button.bounding_box.return_value = {"x": 10, "y": 10, "width": 100, "height": 30}
        mock_button.is_visible.return_value = True
        
        # Test element enhancement
        enhanced_node = await dom_service._enhance_element(mock_button, 0)
        
        # Verify enhancement
        assert enhanced_node.tag_name == "button"
        assert enhanced_node.is_visible is True
    
    def test_dom_serialization(self, dom_service):
        """Test DOM tree serialization for LLM consumption."""
        # Create mock DOM tree
        mock_dom_tree = {
            "nodes": {
                "0": EnhancedDOMNode(
                    node_id=0,
                    tag_name="button",
                    node_type=NodeType.ELEMENT,
                    text_content="Click me",
                    attributes={"id": "submit-btn", "class": "btn-primary"},
                    bounding_box=DOMRect(x=10, y=10, width=100, height=30),
                    is_visible=True,
                    is_clickable=True,
                    is_focusable=True
                )
            }
        }
        
        # Serialize DOM tree
        serialized = dom_service.serialize_dom_tree(mock_dom_tree)
        
        # Verify serialization
        assert isinstance(serialized, str)
        assert "button" in serialized
        assert "Click me" in serialized


class TestToolsIntegration:
    """Test tools integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_navigation_action(self, tools_manager, mock_browser_session):
        """Test navigation action execution."""
        # Mock successful navigation
        mock_browser_session.navigate_to_url.return_value = {
            "success": True,
            "url": "https://example.com"
        }
        
        # Execute navigation action
        result = await tools_manager.navigate_to_url("https://example.com")
        
        # Verify result
        assert isinstance(result, ActionResult)
        assert result.success is True
        assert result.action_type == "navigate_to_url"
    
    @pytest.mark.asyncio
    async def test_element_interaction_actions(self, tools_manager, mock_browser_session):
        """Test element interaction actions."""
        # Mock page and element
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_page.query_selector.return_value = mock_element
        mock_browser_session.get_current_page.return_value = mock_page
        
        # Test click action
        result = await tools_manager.click_element("button#submit")
        
        # Verify result
        assert isinstance(result, ActionResult)
        assert result.action_type == "click_element"
    
    @pytest.mark.asyncio
    async def test_text_input_action(self, tools_manager, mock_browser_session):
        """Test text input action."""
        # Mock page and element
        mock_page = AsyncMock()
        mock_element = AsyncMock()
        mock_page.query_selector.return_value = mock_element
        mock_browser_session.get_current_page.return_value = mock_page
        
        # Test type action
        result = await tools_manager.type_text("input#username", "testuser")
        
        # Verify result
        assert isinstance(result, ActionResult)
        assert result.action_type == "type_text"
    
    @pytest.mark.asyncio
    async def test_screenshot_action(self, tools_manager, mock_browser_session):
        """Test screenshot action."""
        # Mock screenshot
        mock_browser_session.take_screenshot.return_value = "/tmp/test_screenshot.png"
        
        # Test screenshot action
        result = await tools_manager.take_screenshot()
        
        # Verify result
        assert isinstance(result, ActionResult)
        assert result.success is True
        assert result.action_type == "take_screenshot"
        assert "/tmp/test_screenshot.png" in str(result.result)
    
    def test_action_registry(self, tools_manager):
        """Test action registry functionality."""
        # Get available actions
        actions = tools_manager.get_available_actions()
        
        # Verify actions are registered
        assert isinstance(actions, list)
        assert len(actions) > 0
        
        # Check action structure
        for action in actions:
            assert "name" in action
            assert "description" in action
            assert "requires_browser" in action


class TestErrorHandlingIntegration:
    """Test error handling integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_browser_error_propagation(self, tools_manager, mock_browser_session):
        """Test browser error propagation through tools."""
        # Mock browser error
        mock_browser_session.navigate_to_url.side_effect = BrowserError(
            "Navigation failed",
            long_term_memory="Browser navigation error",
            short_term_memory="Page failed to load"
        )
        
        # Test error handling
        result = await tools_manager.navigate_to_url("https://invalid-url.com")
        
        # Verify error handling
        assert result.success is False
        assert "Navigation failed" in result.error
    
    @pytest.mark.asyncio
    async def test_dom_error_handling(self, dom_service, mock_page):
        """Test DOM error handling."""
        # Mock DOM extraction error
        mock_page.query_selector_all.side_effect = Exception("DOM extraction failed")
        
        # Test error handling
        with pytest.raises(Exception):
            await dom_service.get_dom_tree()
    
    @pytest.mark.asyncio
    async def test_element_not_found_handling(self, tools_manager, mock_browser_session):
        """Test element not found error handling."""
        # Mock page with no elements
        mock_page = AsyncMock()
        mock_page.query_selector.return_value = None
        mock_browser_session.get_current_page.return_value = mock_page
        
        # Test element interaction
        result = await tools_manager.click_element("nonexistent#element")
        
        # Verify error handling
        assert result.success is False
        assert "not found" in result.error.lower()


class TestPerformanceIntegration:
    """Test performance integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_dom_extraction_performance(self, dom_service, mock_page):
        """Test DOM extraction performance."""
        # Mock large DOM
        mock_elements = [AsyncMock() for _ in range(100)]
        mock_page.query_selector_all.return_value = mock_elements
        
        # Measure extraction time
        start_time = time.time()
        await dom_service.get_dom_tree()
        extraction_time = time.time() - start_time
        
        # Should complete quickly with mocked elements
        assert extraction_time < 2.0
    
    @pytest.mark.asyncio
    async def test_action_execution_performance(self, tools_manager, mock_browser_session):
        """Test action execution performance."""
        # Mock quick navigation
        mock_browser_session.navigate_to_url.return_value = {"success": True}
        
        # Measure execution time
        start_time = time.time()
        result = await tools_manager.navigate_to_url("https://example.com")
        execution_time = time.time() - start_time
        
        # Should execute quickly
        assert execution_time < 1.0
        assert result.success is True
    
    def test_memory_efficiency(self, dom_service, tools_manager):
        """Test memory efficiency of components."""
        # Test that components don't hold excessive references
        import gc
        
        # Force garbage collection
        gc.collect()
        
        # Components should be properly initialized
        assert dom_service is not None
        assert tools_manager is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
