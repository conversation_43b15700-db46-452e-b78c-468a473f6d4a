#!/usr/bin/env python3
"""
Integration test runner for AI Sourcing Agent.
Based on browser-use testing patterns for production readiness.
"""

import asyncio
import os
import sys
import time
import argparse
from pathlib import Path
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import pytest


class IntegrationTestRunner:
    """Integration test runner with comprehensive reporting."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_tests(
        self,
        test_patterns: List[str] = None,
        markers: List[str] = None,
        verbose: bool = True,
        capture: str = "no",
        parallel: bool = False
    ) -> Dict[str, Any]:
        """Run integration tests with specified parameters."""
        self.start_time = time.time()
        
        # Build pytest arguments
        args = []
        
        # Test patterns
        if test_patterns:
            args.extend(test_patterns)
        else:
            args.append("tests/integration/")
        
        # Markers
        if markers:
            for marker in markers:
                args.extend(["-m", marker])
        
        # Verbosity
        if verbose:
            args.append("-v")
        
        # Capture
        args.extend(["-s"] if capture == "no" else [])
        
        # Parallel execution
        if parallel:
            args.extend(["-n", "auto"])
        
        # Output format
        args.extend([
            "--tb=short",
            "--strict-markers",
            "--strict-config"
        ])
        
        # Run tests
        print(f"Running integration tests with args: {' '.join(args)}")
        exit_code = pytest.main(args)
        
        self.end_time = time.time()
        
        # Collect results
        self.test_results = {
            "exit_code": exit_code,
            "execution_time": self.end_time - self.start_time,
            "success": exit_code == 0
        }
        
        return self.test_results
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance-specific tests."""
        print("Running performance tests...")
        
        return self.run_tests(
            markers=["performance"],
            verbose=True,
            capture="no"
        )
    
    def run_browser_tests(self) -> Dict[str, Any]:
        """Run browser-specific tests."""
        print("Running browser integration tests...")
        
        return self.run_tests(
            markers=["browser"],
            verbose=True,
            capture="no"
        )
    
    def run_llm_tests(self) -> Dict[str, Any]:
        """Run LLM-specific tests."""
        print("Running LLM integration tests...")
        
        return self.run_tests(
            markers=["llm"],
            verbose=True,
            capture="no"
        )
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests."""
        print("Running all integration tests...")
        
        return self.run_tests(
            test_patterns=["tests/integration/"],
            verbose=True,
            capture="no"
        )
    
    def generate_report(self) -> str:
        """Generate test execution report."""
        if not self.test_results:
            return "No test results available."
        
        report = []
        report.append("=" * 60)
        report.append("INTEGRATION TEST REPORT")
        report.append("=" * 60)
        report.append(f"Execution Time: {self.test_results['execution_time']:.2f} seconds")
        report.append(f"Exit Code: {self.test_results['exit_code']}")
        report.append(f"Success: {'✓' if self.test_results['success'] else '✗'}")
        report.append("=" * 60)
        
        return "\n".join(report)


def setup_test_environment():
    """Setup test environment variables and configuration."""
    # Set test environment
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["ANONYMIZED_TELEMETRY"] = "false"
    
    # Disable real browser for most tests
    os.environ["HEADLESS_BROWSER"] = "true"
    
    # Set test timeouts
    os.environ["TEST_TIMEOUT"] = "30"
    os.environ["STEP_TIMEOUT"] = "10"
    
    print("Test environment configured.")


def main():
    """Main test runner entry point."""
    parser = argparse.ArgumentParser(description="AI Sourcing Agent Integration Test Runner")
    
    parser.add_argument(
        "--suite",
        choices=["all", "performance", "browser", "llm", "agent"],
        default="all",
        help="Test suite to run"
    )
    
    parser.add_argument(
        "--pattern",
        nargs="*",
        help="Test file patterns to run"
    )
    
    parser.add_argument(
        "--marker",
        nargs="*",
        help="Pytest markers to filter tests"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        default=True,
        help="Verbose output"
    )
    
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    
    parser.add_argument(
        "--capture",
        choices=["yes", "no"],
        default="no",
        help="Capture stdout/stderr"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate detailed report"
    )
    
    args = parser.parse_args()
    
    # Setup environment
    setup_test_environment()
    
    # Create test runner
    runner = IntegrationTestRunner()
    
    # Run tests based on suite
    if args.suite == "all":
        results = runner.run_all_tests()
    elif args.suite == "performance":
        results = runner.run_performance_tests()
    elif args.suite == "browser":
        results = runner.run_browser_tests()
    elif args.suite == "llm":
        results = runner.run_llm_tests()
    elif args.suite == "agent":
        results = runner.run_tests(
            test_patterns=["tests/integration/test_enhanced_agent.py"],
            verbose=args.verbose,
            capture=args.capture,
            parallel=args.parallel
        )
    else:
        # Custom run with patterns and markers
        results = runner.run_tests(
            test_patterns=args.pattern,
            markers=args.marker,
            verbose=args.verbose,
            capture=args.capture,
            parallel=args.parallel
        )
    
    # Generate report if requested
    if args.report:
        print("\n" + runner.generate_report())
    
    # Print summary
    print(f"\nTest execution completed in {results['execution_time']:.2f} seconds")
    print(f"Result: {'SUCCESS' if results['success'] else 'FAILURE'}")
    
    # Exit with appropriate code
    sys.exit(results['exit_code'])


if __name__ == "__main__":
    main()
