# Enhanced AI Sourcing Agent - API Reference

This document provides comprehensive API documentation for the enhanced AI Sourcing Agent system.

## 📚 Table of Contents

- [Core Components](#core-components)
- [Configuration API](#configuration-api)
- [Browser Management](#browser-management)
- [LLM Integration](#llm-integration)
- [Tools and Actions](#tools-and-actions)
- [DOM Service](#dom-service)
- [Event System](#event-system)
- [Observability](#observability)

## 🧩 Core Components

### EnhancedAgent

Main orchestration component for task execution.

```python
from src.agents.enhanced_agent import EnhancedAgent

class EnhancedAgent:
    def __init__(
        self,
        task: str,
        llm_provider: BaseLLMProvider,
        browser_manager: BrowserManager,
        tools: Tools,
        config: AgentConfig
    )
    
    async def run(self, max_steps: int = 50) -> str:
        """Execute the agent task with specified maximum steps."""
        
    async def step(self, step_number: int) -> bool:
        """Execute a single step of the task."""
        
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get conversation statistics including token usage and costs."""
```

**Usage Example**:
```python
agent = EnhancedAgent(
    task="Search for information about browser automation",
    llm_provider=gemini_provider,
    browser_manager=browser_manager,
    tools=tools,
    config=agent_config
)

result = await agent.run(max_steps=30)
stats = agent.get_conversation_stats()
```

## ⚙️ Configuration API

### ConfigurationService

Centralized configuration management with environment support.

```python
from src.config.service import ConfigurationService, BrowserProfile, LLMConfig, AgentConfig

class ConfigurationService:
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from files and environment variables."""
        
    def get_browser_profile(self, name: str = "default") -> BrowserProfile:
        """Get browser profile configuration."""
        
    def get_llm_config(self, name: str = "default") -> LLMConfig:
        """Get LLM provider configuration."""
        
    def get_agent_config(self, name: str = "default") -> AgentConfig:
        """Get agent behavior configuration."""
        
    def validate_configuration(self) -> bool:
        """Validate current configuration."""
```

### Configuration Models

```python
class BrowserProfile(BaseModel):
    name: str = "default"
    headless: bool = True
    window_width: int = 1280
    window_height: int = 720
    timeout: float = 30.0
    enable_stealth: bool = True
    proxy: Optional[ProxySettings] = None

class LLMConfig(BaseModel):
    name: str = "default"
    provider: str = "gemini"
    model: str = "gemini-2.0-flash-exp"
    api_key: str
    temperature: float = 0.7
    max_tokens: int = 8192
    enable_cost_tracking: bool = True

class AgentConfig(BaseModel):
    name: str = "default"
    max_steps: int = 50
    step_timeout: float = 60.0
    enable_screenshots: bool = False
    max_retries: int = 3
    enable_telemetry: bool = True
```

## 🌐 Browser Management

### BrowserManager

Browser session management with event-driven architecture.

```python
from src.browser.browser_manager import BrowserManager

class BrowserManager:
    def __init__(self, profile: BrowserProfile)
    
    async def initialize(self) -> None:
        """Initialize browser with configuration."""
        
    async def navigate_to(self, url: str) -> None:
        """Navigate to specified URL with event emission."""
        
    async def create_new_tab(self) -> str:
        """Create new browser tab and return tab ID."""
        
    async def switch_to_tab(self, tab_id: str) -> None:
        """Switch to specified tab."""
        
    async def close_tab(self, tab_id: str) -> None:
        """Close specified tab."""
        
    def get_current_page(self) -> Page:
        """Get current Playwright page object."""
        
    async def take_screenshot(self, path: Optional[str] = None) -> bytes:
        """Take screenshot of current page."""
        
    async def close(self) -> None:
        """Close browser and cleanup resources."""
```

**Usage Example**:
```python
browser_manager = BrowserManager(browser_profile)
await browser_manager.initialize()
await browser_manager.navigate_to("https://example.com")
screenshot = await browser_manager.take_screenshot()
await browser_manager.close()
```

## 🤖 LLM Integration

### BaseLLMProvider

Abstract base class for LLM providers.

```python
from src.llm.base import BaseLLMProvider
from src.llm.messages import BaseMessage
from src.llm.views import LLMResponse

class BaseLLMProvider:
    async def generate_response(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> LLMResponse:
        """Generate response from messages."""
        
    def get_capabilities(self) -> Dict[str, Any]:
        """Get provider capabilities."""
        
    def get_cost_info(self) -> Dict[str, Any]:
        """Get cost tracking information."""
```

### GeminiProvider

Google Gemini LLM provider implementation.

```python
from src.llm.providers.gemini_provider import GeminiProvider

class GeminiProvider(BaseLLMProvider):
    def __init__(
        self,
        api_key: str,
        model: str = "gemini-2.0-flash-exp",
        temperature: float = 0.7,
        max_tokens: int = 8192
    )
    
    async def ainvoke(self, messages: List[BaseMessage]) -> ChatInvokeCompletion:
        """Browser-use compatible invoke method."""
```

### Message System

```python
from src.llm.messages import BaseMessage, create_text_message, create_image_message

# Create messages
text_message = create_text_message("Hello, world!")
image_message = create_image_message("path/to/image.png", "Describe this image")

# Message structure
class BaseMessage(BaseModel):
    role: Literal["system", "user", "assistant"]
    content: Union[str, List[ContentPart]]
    timestamp: float = Field(default_factory=time.time)
```

## 🛠 Tools and Actions

### Tools Service

Extensible action system with built-in browser actions.

```python
from src.tools.service import Tools
from src.tools.registry import ActionResult

class Tools:
    def __init__(
        self,
        browser_manager: Optional[BrowserManager] = None,
        dom_service: Optional[DOMService] = None,
        llm_provider: Optional[BaseLLMProvider] = None
    )
    
    async def execute_action(
        self,
        action_name: str,
        parameters: Dict[str, Any]
    ) -> ActionResult:
        """Execute registered action with parameters."""
        
    def get_available_actions(self) -> List[str]:
        """Get list of available action names."""
        
    def get_action_info(self, action_name: str) -> Dict[str, Any]:
        """Get detailed information about an action."""
```

### Action Registry

Decorator-based action registration system.

```python
from src.tools.registry import Registry
from pydantic import BaseModel, Field

# Create registry
registry = Registry()

# Define parameter model
class NavigateParams(BaseModel):
    url: str = Field(..., description="URL to navigate to")

# Register action
@registry.action(
    "Navigate to a specific URL",
    param_model=NavigateParams,
    category="navigation",
    requires_browser=True
)
async def navigate(params: NavigateParams, browser_manager: BrowserManager):
    await browser_manager.navigate_to(params.url)
    return ActionResult(
        success=True,
        action_type="navigate",
        extracted_content=f"Navigated to {params.url}"
    )
```

### Built-in Actions

Available built-in actions:

- `navigate`: Navigate to URL
- `click`: Click element by selector or index
- `type`: Type text into input field
- `extract_text`: Extract text from elements
- `extract_links`: Extract all links from page
- `extract_images`: Extract all images from page
- `scroll`: Scroll page or element
- `wait`: Wait for element or condition
- `search_google`: Perform Google search

## 🔍 DOM Service

Enhanced DOM interaction and element detection.

```python
from src.dom.service import DOMService, EnhancedDOMNode

class DOMService:
    def __init__(self, page: Page)
    
    async def extract_dom_tree(self) -> List[EnhancedDOMNode]:
        """Extract complete DOM tree with enhanced information."""
        
    async def get_interactive_elements(self) -> Dict[int, EnhancedDOMNode]:
        """Get all interactive elements with index mapping."""
        
    async def find_elements(self, selector: str) -> List[EnhancedDOMNode]:
        """Find elements by CSS selector."""
        
    async def click_element(
        self,
        element: Union[EnhancedDOMNode, int, str]
    ) -> bool:
        """Click element by node, index, or selector."""
        
    async def type_text(
        self,
        element: Union[EnhancedDOMNode, int, str],
        text: str
    ) -> bool:
        """Type text into element."""
        
    async def extract_text(self, selector: str = "body") -> str:
        """Extract text content from elements."""
```

### DOM Node Structure

```python
class EnhancedDOMNode(BaseModel):
    tag_name: str
    attributes: Dict[str, str]
    text_content: str
    is_interactive: bool
    element_index: Optional[int]
    xpath: str
    css_selector: str
    bounding_box: Optional[DOMRect]
    accessibility_info: Dict[str, Any]
    children: List['EnhancedDOMNode']
```

## 📡 Event System

Event-driven architecture for component communication.

```python
from src.events.event_bus import EventBus
from src.events.browser_events import NavigateToUrlEvent, ClickElementEvent

# Global event bus
from src.events import default_event_bus

# Subscribe to events
async def handle_navigation(event: NavigateToUrlEvent):
    print(f"Navigated to: {event.url}")

default_event_bus.subscribe(NavigateToUrlEvent, handle_navigation)

# Emit events
event = NavigateToUrlEvent(url="https://example.com")
await default_event_bus.emit(event)
```

### Available Events

Browser Events:
- `BrowserLaunchEvent`: Browser started
- `BrowserStoppedEvent`: Browser stopped
- `NavigateToUrlEvent`: Navigation initiated
- `NavigationCompleteEvent`: Navigation completed
- `ClickElementEvent`: Element clicked
- `TypeTextEvent`: Text typed
- `TabCreatedEvent`: New tab created
- `TabClosedEvent`: Tab closed

## 📊 Observability

Comprehensive observability and telemetry system.

```python
from src.observability import observability_service, observe, time_execution_async

# Record metrics
observability_service.record_metric("custom_metric", 42.0, "count")

# Record telemetry
observability_service.record_telemetry(
    "user_action",
    "interaction",
    {"action": "search", "query": "test"}
)

# Tracing decorators
@observe(name="custom_operation", span_type="TOOL")
@time_execution_async("processing")
async def custom_operation():
    # Your code here
    return "result"

# Manual tracing
trace_id = observability_service.start_trace()
span = observability_service.start_span("operation_name")
# ... do work ...
observability_service.finish_span(span.span_id)
```

### Observability Data

```python
# Get summaries
metrics_summary = observability_service.get_metrics_summary()
trace_summary = observability_service.get_trace_summary()

# Export data
export_data = observability_service.export_data()

# Clear data
observability_service.clear_data()
```

## 🔧 Utility Functions

### Logging

```python
from src.utils.logging import get_logger, setup_logging

# Get logger
logger = get_logger(__name__)

# Setup logging
setup_logging(level="INFO", log_file="app.log")
```

### Configuration Helpers

```python
from src.config import config_service

# Quick access to configurations
browser_profile = config_service.get_browser_profile()
llm_config = config_service.get_llm_config()
agent_config = config_service.get_agent_config()
```

## 🚀 Quick Start Example

Complete example using the enhanced API:

```python
import asyncio
from src.agents.enhanced_agent import EnhancedAgent
from src.browser.browser_manager import BrowserManager
from src.llm.providers.gemini_provider import GeminiProvider
from src.tools.service import Tools
from src.config import config_service

async def main():
    # Load configurations
    browser_profile = config_service.get_browser_profile("default")
    llm_config = config_service.get_llm_config("default")
    agent_config = config_service.get_agent_config("default")
    
    # Initialize components
    browser_manager = BrowserManager(browser_profile)
    await browser_manager.initialize()
    
    llm_provider = GeminiProvider(
        api_key=llm_config.api_key,
        model=llm_config.model
    )
    
    tools = Tools(
        browser_manager=browser_manager,
        llm_provider=llm_provider
    )
    
    # Create and run agent
    agent = EnhancedAgent(
        task="Search for information about browser automation",
        llm_provider=llm_provider,
        browser_manager=browser_manager,
        tools=tools,
        config=agent_config
    )
    
    result = await agent.run()
    print(f"Task result: {result}")
    
    # Get statistics
    stats = agent.get_conversation_stats()
    print(f"Conversation stats: {stats}")
    
    # Cleanup
    await browser_manager.close()

if __name__ == "__main__":
    asyncio.run(main())
```

This API reference provides comprehensive documentation for all major components of the enhanced AI Sourcing Agent system. Each component follows browser-use patterns for production reliability and enterprise scalability.
