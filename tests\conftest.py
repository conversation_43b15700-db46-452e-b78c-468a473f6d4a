"""
Pytest configuration and fixtures for the enhanced AI Sourcing Agent tests.
"""

import asyncio
import os
import pytest
import tempfile
from pathlib import Path
from typing import Generator, AsyncGenerator

# Set test environment variables
os.environ["AI_SOURCING_LOGGING_LEVEL"] = "DEBUG"
os.environ["ANONYMIZED_TELEMETRY"] = "false"
os.environ["AI_SOURCING_HEADLESS"] = "true"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_config_dir(temp_dir) -> Path:
    """Create a test configuration directory."""
    config_dir = temp_dir / "config"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    # Set environment variable to use test config directory
    os.environ["AI_SOURCING_CONFIG_DIR"] = str(config_dir)
    
    return config_dir


@pytest.fixture
def mock_api_keys():
    """Set mock API keys for testing."""
    original_keys = {}
    
    # Store original values
    for key in ["GOOGLE_API_KEY", "GEMINI_API_KEY", "OPENAI_API_KEY", "ANTHROPIC_API_KEY"]:
        original_keys[key] = os.environ.get(key)
    
    # Set test values
    os.environ["GOOGLE_API_KEY"] = "test-google-key"
    os.environ["GEMINI_API_KEY"] = "test-gemini-key"
    os.environ["OPENAI_API_KEY"] = "test-openai-key"
    os.environ["ANTHROPIC_API_KEY"] = "test-anthropic-key"
    
    yield
    
    # Restore original values
    for key, value in original_keys.items():
        if value is not None:
            os.environ[key] = value
        elif key in os.environ:
            del os.environ[key]


@pytest.fixture(autouse=True)
def setup_test_environment(test_config_dir, mock_api_keys):
    """Set up test environment for all tests."""
    # This fixture runs automatically for all tests
    pass


@pytest.fixture
async def clean_observability():
    """Clean observability data before and after tests."""
    from src.observability import observability_service
    
    # Clean before test
    observability_service.clear_data()
    
    yield
    
    # Clean after test
    observability_service.clear_data()


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "browser: mark test as requiring browser"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add integration marker to integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add browser marker to tests that use browser
        if "browser" in str(item.fspath) or "browser" in item.name.lower():
            item.add_marker(pytest.mark.browser)
        
        # Add slow marker to integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.slow)


# Async test configuration
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture(scope="session")
def anyio_backend():
    """Configure anyio backend for async tests."""
    return "asyncio"
