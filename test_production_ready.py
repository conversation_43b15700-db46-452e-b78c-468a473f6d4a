#!/usr/bin/env python3
"""
Production-Ready Test Suite for Enhanced AI Sourcing Agent
Tests real-world scenarios with actual browser automation and LLM integration
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Any
import traceback

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class ProductionTestSuite:
    """Comprehensive production test suite."""
    
    def __init__(self):
        self.results = []
        self.start_time = time.time()
        self.setup_logging()
    
    def setup_logging(self):
        """Setup production logging."""
        from src.utils.logging_config import setup_logging
        self.logger = setup_logging(log_level="INFO")
        self.logger.info("🚀 Starting Production Test Suite")
    
    async def test_real_browser_automation(self) -> Dict[str, Any]:
        """Test real browser automation with complex interactions."""
        test_name = "Real Browser Automation"
        self.logger.info(f"🌐 Starting {test_name}")
        
        try:
            from playwright.async_api import async_playwright
            from src.browser.enhanced_browser import EnhancedBrowserSession
            from src.dom.enhanced_dom import EnhancedDOMService
            
            # Test with real browser
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=False)  # Visible browser
                page = await browser.new_page()
                
                # Navigate to a complex website
                await page.goto("https://httpbin.org/forms/post")
                self.logger.info("✓ Navigated to test form")
                
                # Test form interaction
                await page.fill('input[name="custname"]', "Test Customer")
                await page.fill('input[name="custtel"]', "************")
                await page.fill('input[name="custemail"]', "<EMAIL>")
                await page.select_option('select[name="size"]', "medium")
                await page.check('input[name="topping"][value="bacon"]')
                await page.fill('textarea[name="comments"]', "This is a test automation")
                
                self.logger.info("✓ Filled complex form with multiple input types")
                
                # Test DOM extraction
                dom_service = EnhancedDOMService()
                
                # Get page content
                content = await page.content()
                title = await page.title()
                url = page.url
                
                self.logger.info(f"✓ Extracted page data: {title} at {url}")
                
                # Test screenshot
                screenshot = await page.screenshot(path="test_screenshot.png")
                self.logger.info("✓ Captured screenshot")
                
                # Test JavaScript execution
                result = await page.evaluate("""
                    () => {
                        const inputs = document.querySelectorAll('input, select, textarea');
                        return Array.from(inputs).map(el => ({
                            tag: el.tagName,
                            type: el.type || 'N/A',
                            name: el.name || 'N/A',
                            value: el.value || 'N/A'
                        }));
                    }
                """)
                
                self.logger.info(f"✓ Executed JavaScript, found {len(result)} form elements")
                
                await browser.close()
                
                return {
                    "success": True,
                    "details": {
                        "page_title": title,
                        "form_elements": len(result),
                        "screenshot_captured": os.path.exists("test_screenshot.png"),
                        "form_data_entered": True
                    }
                }
                
        except Exception as e:
            self.logger.error(f"❌ {test_name} failed: {e}")
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def test_llm_integration_with_vision(self) -> Dict[str, Any]:
        """Test LLM integration with vision capabilities."""
        test_name = "LLM Integration with Vision"
        self.logger.info(f"🧠 Starting {test_name}")
        
        try:
            from src.llm.enhanced_llm import EnhancedLLMProvider, LLMMessage
            from src.config.service import CONFIG
            
            # Test with Gemini (supports vision)
            if hasattr(CONFIG, 'GEMINI_API_KEY') and CONFIG.GEMINI_API_KEY:
                try:
                    import google.generativeai as genai
                    
                    genai.configure(api_key=CONFIG.GEMINI_API_KEY)
                    model = genai.GenerativeModel('gemini-2.0-flash-exp')
                    
                    # Test text generation
                    response = model.generate_content(
                        "Explain the importance of browser automation in AI sourcing. Be concise but comprehensive."
                    )
                    
                    self.logger.info("✓ Generated text response from Gemini")
                    self.logger.info(f"Response preview: {response.text[:100]}...")
                    
                    # Test with image if screenshot exists
                    if os.path.exists("test_screenshot.png"):
                        from PIL import Image
                        
                        image = Image.open("test_screenshot.png")
                        vision_response = model.generate_content([
                            "Analyze this screenshot and describe what you see. Focus on form elements and user interface components.",
                            image
                        ])
                        
                        self.logger.info("✓ Generated vision response from Gemini")
                        self.logger.info(f"Vision analysis preview: {vision_response.text[:100]}...")
                        
                        return {
                            "success": True,
                            "details": {
                                "text_response_length": len(response.text),
                                "vision_response_length": len(vision_response.text),
                                "model_used": "gemini-2.0-flash-exp",
                                "vision_capable": True
                            }
                        }
                    else:
                        return {
                            "success": True,
                            "details": {
                                "text_response_length": len(response.text),
                                "model_used": "gemini-2.0-flash-exp",
                                "vision_capable": False,
                                "note": "No screenshot available for vision test"
                            }
                        }
                        
                except Exception as e:
                    self.logger.warning(f"Gemini test failed: {e}")
            
            # Test with OpenAI if available
            if hasattr(CONFIG, 'OPENAI_API_KEY') and CONFIG.OPENAI_API_KEY:
                try:
                    import openai
                    
                    client = openai.OpenAI(api_key=CONFIG.OPENAI_API_KEY)
                    
                    response = client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=[
                            {"role": "user", "content": "Explain the key benefits of using enhanced browser automation for AI-powered sourcing tasks. Be specific about technical advantages."}
                        ],
                        max_tokens=200
                    )
                    
                    self.logger.info("✓ Generated response from OpenAI")
                    self.logger.info(f"Response preview: {response.choices[0].message.content[:100]}...")
                    
                    return {
                        "success": True,
                        "details": {
                            "response_length": len(response.choices[0].message.content),
                            "model_used": "gpt-4o-mini",
                            "tokens_used": response.usage.total_tokens,
                            "provider": "openai"
                        }
                    }
                    
                except Exception as e:
                    self.logger.warning(f"OpenAI test failed: {e}")
            
            return {"success": False, "error": "No LLM providers available or configured"}
            
        except Exception as e:
            self.logger.error(f"❌ {test_name} failed: {e}")
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def test_advanced_dom_processing(self) -> Dict[str, Any]:
        """Test advanced DOM processing with accessibility features."""
        test_name = "Advanced DOM Processing"
        self.logger.info(f"🔍 Starting {test_name}")
        
        try:
            from playwright.async_api import async_playwright
            from src.dom.enhanced_dom import EnhancedDOMService, EnhancedDOMNode, NodeType
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # Navigate to a complex page
                await page.goto("https://example.com")
                
                # Test accessibility tree extraction
                accessibility_tree = await page.accessibility.snapshot()
                self.logger.info(f"✓ Extracted accessibility tree with {len(str(accessibility_tree))} characters")
                
                # Test element analysis
                elements = await page.query_selector_all("*")
                self.logger.info(f"✓ Found {len(elements)} DOM elements")
                
                # Test interactive element detection
                interactive_elements = await page.evaluate("""
                    () => {
                        const interactive = [];
                        const selectors = ['button', 'input', 'select', 'textarea', 'a[href]', '[onclick]', '[role="button"]'];
                        
                        selectors.forEach(selector => {
                            document.querySelectorAll(selector).forEach(el => {
                                const rect = el.getBoundingClientRect();
                                if (rect.width > 0 && rect.height > 0) {
                                    interactive.push({
                                        tag: el.tagName,
                                        type: el.type || 'N/A',
                                        text: el.textContent?.trim().substring(0, 50) || 'N/A',
                                        visible: true,
                                        clickable: true
                                    });
                                }
                            });
                        });
                        
                        return interactive;
                    }
                """)
                
                self.logger.info(f"✓ Identified {len(interactive_elements)} interactive elements")
                
                # Test DOM node creation
                dom_service = EnhancedDOMService()
                
                sample_nodes = []
                for i, elem_data in enumerate(interactive_elements[:5]):  # Test first 5
                    node = EnhancedDOMNode(
                        node_id=i,
                        tag_name=elem_data['tag'].lower(),
                        node_type=NodeType.ELEMENT,
                        text_content=elem_data['text'],
                        is_visible=elem_data['visible'],
                        is_clickable=elem_data['clickable']
                    )
                    sample_nodes.append(node)
                
                self.logger.info(f"✓ Created {len(sample_nodes)} enhanced DOM nodes")
                
                await browser.close()
                
                return {
                    "success": True,
                    "details": {
                        "total_elements": len(elements),
                        "interactive_elements": len(interactive_elements),
                        "accessibility_tree_size": len(str(accessibility_tree)),
                        "enhanced_nodes_created": len(sample_nodes)
                    }
                }
                
        except Exception as e:
            self.logger.error(f"❌ {test_name} failed: {e}")
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def test_event_driven_architecture(self) -> Dict[str, Any]:
        """Test event-driven architecture with real events."""
        test_name = "Event-Driven Architecture"
        self.logger.info(f"📡 Starting {test_name}")
        
        try:
            from src.events import default_event_bus, BrowserEvent, AgentEvent
            
            events_received = []
            
            # Setup event handlers
            async def browser_event_handler(event_data):
                events_received.append(("browser", event_data))
                self.logger.info(f"✓ Received browser event: {event_data.get('type', 'unknown')}")
            
            async def agent_event_handler(event_data):
                events_received.append(("agent", event_data))
                self.logger.info(f"✓ Received agent event: {event_data.get('type', 'unknown')}")
            
            # Subscribe to events
            default_event_bus.subscribe('browser_event', browser_event_handler)
            default_event_bus.subscribe('agent_event', agent_event_handler)
            
            # Emit test events
            await default_event_bus.emit('browser_event', {
                'type': 'navigation',
                'url': 'https://example.com',
                'timestamp': time.time(),
                'success': True
            })
            
            await default_event_bus.emit('agent_event', {
                'type': 'task_start',
                'task_id': 'test_task_001',
                'timestamp': time.time(),
                'details': {'action': 'navigate_and_extract'}
            })
            
            await default_event_bus.emit('browser_event', {
                'type': 'element_interaction',
                'element': {'tag': 'button', 'text': 'Submit'},
                'action': 'click',
                'timestamp': time.time()
            })
            
            # Wait for async event processing
            await asyncio.sleep(0.2)
            
            self.logger.info(f"✓ Processed {len(events_received)} events")
            
            return {
                "success": True,
                "details": {
                    "events_emitted": 3,
                    "events_received": len(events_received),
                    "event_types": [event[1].get('type') for event in events_received]
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ {test_name} failed: {e}")
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def test_comprehensive_error_handling(self) -> Dict[str, Any]:
        """Test comprehensive error handling and recovery."""
        test_name = "Comprehensive Error Handling"
        self.logger.info(f"🛡️ Starting {test_name}")
        
        try:
            from src.utils.exceptions import BrowserError, LLMError, TimeoutError, AccessibilityError
            from playwright.async_api import async_playwright
            
            error_scenarios = []
            
            # Test browser error handling
            try:
                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=True)
                    page = await browser.new_page()
                    
                    # Test timeout handling
                    try:
                        await page.goto("https://httpstat.us/408", timeout=1000)  # Will timeout
                    except Exception as e:
                        timeout_error = TimeoutError(
                            operation="page_navigation",
                            timeout=1.0,
                            message=f"Navigation timeout: {str(e)}"
                        )
                        error_scenarios.append(("timeout", timeout_error))
                        self.logger.info("✓ Timeout error handled correctly")
                    
                    # Test element not found
                    try:
                        await page.click("#non-existent-element", timeout=1000)
                    except Exception as e:
                        browser_error = BrowserError(
                            "Element not found",
                            long_term_memory="Attempted to click non-existent element",
                            short_term_memory=f"Selector: #non-existent-element, Error: {str(e)}"
                        )
                        error_scenarios.append(("element_not_found", browser_error))
                        self.logger.info("✓ Element not found error handled correctly")
                    
                    await browser.close()
            
            except Exception as e:
                browser_error = BrowserError(f"Browser initialization failed: {e}")
                error_scenarios.append(("browser_init", browser_error))
            
            # Test LLM error handling
            try:
                raise LLMError(
                    "Simulated API rate limit",
                    provider="gemini",
                    model="gemini-2.0-flash-exp",
                    context={"rate_limit": True, "retry_after": 60}
                )
            except LLMError as e:
                error_scenarios.append(("llm_rate_limit", e))
                self.logger.info("✓ LLM rate limit error handled correctly")
            
            # Test accessibility error
            try:
                raise AccessibilityError(
                    "Accessibility tree extraction failed",
                    accessibility_info={"tree_depth": 0, "elements_found": 0}
                )
            except AccessibilityError as e:
                error_scenarios.append(("accessibility", e))
                self.logger.info("✓ Accessibility error handled correctly")
            
            return {
                "success": True,
                "details": {
                    "error_scenarios_tested": len(error_scenarios),
                    "error_types": [scenario[0] for scenario in error_scenarios],
                    "all_errors_handled": True
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ {test_name} failed: {e}")
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all production tests."""
        self.logger.info("🎯 Starting Production Test Suite")
        
        tests = [
            ("Real Browser Automation", self.test_real_browser_automation),
            ("LLM Integration with Vision", self.test_llm_integration_with_vision),
            ("Advanced DOM Processing", self.test_advanced_dom_processing),
            ("Event-Driven Architecture", self.test_event_driven_architecture),
            ("Comprehensive Error Handling", self.test_comprehensive_error_handling),
        ]
        
        results = {}
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"🧪 RUNNING: {test_name}")
            self.logger.info(f"{'='*60}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                if result.get("success", False):
                    passed += 1
                    self.logger.info(f"✅ {test_name} PASSED")
                else:
                    failed += 1
                    self.logger.error(f"❌ {test_name} FAILED")
                    if "error" in result:
                        self.logger.error(f"Error: {result['error']}")
                        
            except Exception as e:
                failed += 1
                results[test_name] = {
                    "success": False,
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
                self.logger.error(f"❌ {test_name} FAILED with exception: {e}")
        
        execution_time = time.time() - self.start_time
        
        summary = {
            "total_tests": len(tests),
            "passed": passed,
            "failed": failed,
            "success_rate": (passed / len(tests)) * 100,
            "execution_time": execution_time,
            "results": results
        }
        
        self.logger.info(f"\n{'='*70}")
        self.logger.info("🎯 PRODUCTION TEST SUITE SUMMARY")
        self.logger.info(f"{'='*70}")
        self.logger.info(f"Total Tests: {summary['total_tests']}")
        self.logger.info(f"Passed: {summary['passed']}")
        self.logger.info(f"Failed: {summary['failed']}")
        self.logger.info(f"Success Rate: {summary['success_rate']:.1f}%")
        self.logger.info(f"Execution Time: {summary['execution_time']:.2f} seconds")
        
        if failed == 0:
            self.logger.info("🎉 ALL PRODUCTION TESTS PASSED!")
            self.logger.info("🚀 System is production-ready!")
        elif passed >= 3:
            self.logger.info("✅ SYSTEM MOSTLY OPERATIONAL!")
            self.logger.info("🔧 Minor issues detected but core functionality is solid")
        else:
            self.logger.warning("⚠️ Critical issues detected. Review failed tests.")
        
        return summary

async def main():
    """Run the production test suite."""
    suite = ProductionTestSuite()
    results = await suite.run_all_tests()
    
    # Save detailed results
    with open("production_test_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    return 0 if results["failed"] == 0 else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
