# Enhanced AI Sourcing Agent Architecture

This document describes the enhanced architecture of the AI Sourcing Agent system, built with browser-use patterns for production reliability and enterprise scalability.

## 🏗 System Overview

The enhanced system follows browser-use architectural patterns with a focus on:
- **Production Reliability**: Comprehensive error handling and recovery
- **Enterprise Scalability**: Event-driven architecture with observability
- **Maintainability**: Modular design with clear separation of concerns
- **Extensibility**: Plugin-based architecture with action registry

## 📊 Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                    Enhanced AI Sourcing Agent                   │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │   Enhanced      │    │   Browser       │    │   LLM       │  │
│  │   Agent         │◄──►│   Manager       │    │   Providers │  │
│  └─────────────────┘    └─────────────────┘    └─────────────┘  │
│           │                       │                       │      │
│           ▼                       ▼                       ▼      │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │   Tools &       │    │   DOM           │    │   Message   │  │
│  │   Actions       │    │   Service       │    │   Manager   │  │
│  └─────────────────┘    └─────────────────┘    └─────────────┘  │
│           │                       │                       │      │
│           └───────────────────────┼───────────────────────┘      │
│                                   ▼                              │
│                      ┌─────────────────┐                        │
│                      │   Event Bus &   │                        │
│                      │   Observability │                        │
│                      └─────────────────┘                        │
│                                   │                              │
├───────────────────────────────────┼──────────────────────────────┤
│                Configuration      │      Observability           │
│                                   ▼                              │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │   Config        │    │   Metrics &     │    │   Telemetry │  │
│  │   Service       │    │   Tracing       │    │   Events    │  │
│  └─────────────────┘    └─────────────────┘    └─────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 🧩 Core Components

### 1. Enhanced Agent (`src/agents/enhanced_agent.py`)

**Purpose**: Main orchestration component with browser-use patterns

**Key Features**:
- Step-by-step task execution with proper state management
- Message manager integration for conversation handling
- Comprehensive error handling and recovery
- Event emission for observability
- Cost tracking and usage monitoring

**Browser-Use Patterns**:
- Structured message handling with content parts
- Token and cost tracking per conversation
- Step execution with timeout and retry logic
- System message setup and management

```python
class EnhancedAgent:
    def __init__(self, task, llm_provider, browser_manager, tools, config):
        self.message_manager = MessageManager()
        self.observability = observability_service
        
    async def run(self, max_steps=50):
        # Browser-use style step execution
        for step in range(max_steps):
            await self._execute_step(step)
```

### 2. Browser Manager (`src/browser/browser_manager.py`)

**Purpose**: Browser session management with CDP integration

**Key Features**:
- Event-driven navigation with proper event emission
- Tab management and browser ID tracking
- Watchdog system for monitoring browser health
- Resource cleanup and session management
- Proxy and stealth mode support

**Browser-Use Patterns**:
- Browser session lifecycle management
- Event emission for navigation and interactions
- Proper resource cleanup and error handling
- CDP integration for advanced browser control

```python
class BrowserManager:
    async def navigate_to(self, url: str):
        # Emit navigation event
        event = NavigateToUrlEvent(url=url)
        await self.event_bus.emit(event)
        
        # Navigate with error handling
        await self.page.goto(url)
```

### 3. DOM Service (`src/dom/service.py`)

**Purpose**: Enhanced DOM interaction and element detection

**Key Features**:
- Comprehensive DOM tree extraction
- Interactive element identification and indexing
- Accessibility support with ARIA attributes
- Element interaction with proper event emission
- Performance optimizations with caching

**Browser-Use Patterns**:
- Enhanced DOM node representation
- Interactive element indexing
- Accessibility tree integration
- Element serialization for LLM consumption

```python
class DOMService:
    async def extract_dom_tree(self):
        # Extract DOM with browser-use patterns
        dom_nodes = await self._extract_dom_nodes()
        enhanced_nodes = await self._enhance_nodes(dom_nodes)
        await self._identify_interactive_elements(enhanced_nodes)
        return enhanced_nodes
```

### 4. Tools System (`src/tools/`)

**Purpose**: Extensible action system with validation and domain filtering

**Components**:
- **Registry** (`registry.py`): Action registration with validation
- **Service** (`service.py`): Built-in browser actions with extensibility

**Key Features**:
- Decorator-based action registration
- Parameter validation with Pydantic models
- Domain-based action filtering
- Dependency injection for browser/LLM access
- Comprehensive error handling and retries

**Browser-Use Patterns**:
- Action registry with prompt descriptions
- Parameter model validation
- Domain filtering for URL-specific actions
- Structured action results

```python
@tools.action(
    "Navigate to a specific URL",
    param_model=NavigateAction,
    category="navigation",
    requires_browser=True
)
async def navigate(params: NavigateAction, browser_manager: BrowserManager):
    await browser_manager.navigate_to(params.url)
    return ActionResult(success=True, action_type="navigate")
```

### 5. LLM Integration (`src/llm/`)

**Purpose**: Multi-provider LLM integration with cost tracking

**Components**:
- **Base** (`base.py`): Provider abstraction with browser-use patterns
- **Messages** (`messages.py`): Structured message system
- **Views** (`views.py`): Response models and usage tracking
- **Providers**: Gemini, OpenAI, Anthropic implementations

**Key Features**:
- Structured message handling with content parts
- Cost tracking and usage monitoring
- Provider abstraction with consistent interface
- Structured output support
- Error handling and retry logic

**Browser-Use Patterns**:
- ChatInvokeCompletion response format
- Usage tracking with token and cost information
- Provider protocol for consistent interface
- Message content parts for multimodal support

```python
class BaseLLMProvider:
    async def generate_response(self, messages: List[BaseMessage]) -> LLMResponse:
        completion = await self._generate_response(messages)
        self.cost_tracker.add_usage(completion.usage, self.model_name)
        return LLMResponse.from_completion(completion, self.provider_name, self.model_name)
```

### 6. Configuration System (`src/config/`)

**Purpose**: Environment-aware configuration management

**Key Features**:
- Hierarchical configuration (env vars → files → defaults)
- Profile management for different environments
- Configuration validation and migration
- Environment variable support with aliases
- Proxy and security settings

**Browser-Use Patterns**:
- Browser profile configuration
- LLM provider configuration
- Agent behavior configuration
- Environment variable handling

```python
class ConfigurationService:
    def load_config(self) -> Dict[str, Any]:
        config_data = self._load_from_file()
        config_data = self._apply_env_overrides(config_data)
        config_data = self._ensure_defaults(config_data)
        return config_data
```

### 7. Observability System (`src/observability/`)

**Purpose**: Comprehensive observability and telemetry

**Key Features**:
- Distributed tracing with span management
- Metrics collection and aggregation
- Telemetry event recording
- Performance monitoring with decorators
- Debug mode support

**Browser-Use Patterns**:
- Observe decorators for automatic tracing
- Debug-only observability support
- Performance timing decorators
- Structured telemetry events

```python
@observe(name="agent.step", span_type="DEFAULT")
@time_execution_async("step execution")
async def execute_step(self):
    # Automatic tracing and timing
    pass
```

## 🔄 Event-Driven Architecture

### Event Bus (`src/events/event_bus.py`)

The system uses an event-driven architecture for loose coupling:

```python
# Event emission
event = NavigateToUrlEvent(url="https://example.com")
await default_event_bus.emit(event)

# Event subscription
async def handle_navigation(event: NavigateToUrlEvent):
    print(f"Navigated to: {event.url}")

default_event_bus.subscribe(NavigateToUrlEvent, handle_navigation)
```

### Browser Events (`src/events/browser_events.py`)

Comprehensive browser event system:
- Navigation events (NavigateToUrlEvent, NavigationCompleteEvent)
- Interaction events (ClickElementEvent, TypeTextEvent)
- Session events (BrowserLaunchEvent, BrowserStoppedEvent)
- Tab events (TabCreatedEvent, TabClosedEvent)

## 📊 Data Flow

### 1. Task Execution Flow

```
User Task → Enhanced Agent → Message Manager → LLM Provider
     ↓              ↓              ↓              ↓
Action Plan ← Step Execution ← Response ← LLM Response
     ↓
Tools Registry → Browser Manager → DOM Service
     ↓                 ↓              ↓
Action Result ← Browser Action ← Element Interaction
     ↓
Event Bus → Observability → Metrics/Tracing
```

### 2. Configuration Flow

```
Environment Variables → Configuration Service → Profile Loading
         ↓                        ↓                    ↓
    Overrides → File Configuration → Default Values
         ↓                        ↓                    ↓
    Validation → Component Initialization → System Startup
```

### 3. Observability Flow

```
Function Execution → Observe Decorator → Span Creation
        ↓                   ↓               ↓
   Metrics Recording → Observability Service → Data Collection
        ↓                   ↓               ↓
   Telemetry Events → Export/Analysis → Monitoring
```

## 🔧 Browser-Use Pattern Implementation

### 1. Lazy Import System

```python
# src/__init__.py
_LAZY_IMPORTS = {
    'EnhancedAgent': ('src.agents.enhanced_agent', 'EnhancedAgent'),
    'BrowserManager': ('src.browser.browser_manager', 'BrowserManager'),
    # ... more imports
}

def __getattr__(name: str):
    if name in _LAZY_IMPORTS:
        module_path, attr_name = _LAZY_IMPORTS[name]
        module = import_module(module_path)
        return getattr(module, attr_name)
```

### 2. Action Registry Pattern

```python
class Registry:
    def action(self, description: str, **kwargs):
        def decorator(func: Callable):
            normalized_func, param_model = self._normalize_function_signature(func)
            action = RegisteredAction(
                name=func.__name__,
                description=description,
                function=normalized_func,
                param_model=param_model
            )
            self.registry.actions[func.__name__] = action
            return normalized_func
        return decorator
```

### 3. Message Management Pattern

```python
class MessageManager:
    def create_user_message(self, content: str) -> BaseMessage:
        message = BaseMessage(role="user", content=content)
        self.messages.append(message)
        self._update_token_count(message)
        return message
    
    def get_messages_for_llm(self) -> List[BaseMessage]:
        return [self.system_message] + self.messages
```

### 4. Observability Pattern

```python
def observe(name: str = None, **kwargs):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            span = observability_service.start_span(name or func.__name__)
            try:
                result = await func(*args, **kwargs)
                observability_service.finish_span(span.span_id)
                return result
            except Exception as e:
                observability_service.finish_span(span.span_id, error=str(e))
                raise
        return wrapper
    return decorator
```

## 🚀 Production Features

### 1. Error Handling and Recovery
- Comprehensive exception handling at all levels
- Retry logic with exponential backoff
- Graceful degradation for non-critical failures
- Resource cleanup on errors

### 2. Performance Optimization
- Lazy loading of components
- DOM caching and incremental updates
- Connection pooling for HTTP requests
- Memory management and cleanup

### 3. Security and Compliance
- Stealth mode for bot detection avoidance
- Proxy support for network routing
- API key management and validation
- Data sanitization and privacy protection

### 4. Monitoring and Observability
- Distributed tracing for request flow
- Metrics collection for performance monitoring
- Telemetry for usage analytics
- Health checks and system validation

### 5. Configuration Management
- Environment-specific configurations
- Profile-based settings management
- Runtime configuration updates
- Validation and migration support

## 🔄 Migration from Original System

The enhanced system maintains backward compatibility while adding browser-use patterns:

### Key Improvements
1. **Structured Architecture**: Clear separation of concerns with browser-use patterns
2. **Enhanced Error Handling**: Comprehensive error recovery and retry logic
3. **Observability**: Full tracing, metrics, and telemetry support
4. **Configuration**: Environment-aware configuration management
5. **Testing**: Comprehensive test suite with integration tests
6. **Documentation**: Complete documentation with examples

### Migration Path
1. **Gradual Migration**: Components can be migrated incrementally
2. **Backward Compatibility**: Existing APIs remain functional
3. **Enhanced Features**: New features available through enhanced components
4. **Configuration Update**: Update configuration to use new profile system
5. **Testing Integration**: Add tests using new testing framework

This enhanced architecture provides a solid foundation for production browser automation with enterprise-grade reliability, observability, and maintainability.

## 📚 Additional Documentation

- [API Reference](API_REFERENCE.md) - Detailed API documentation
- [Configuration Guide](CONFIGURATION.md) - Configuration options and examples
- [Testing Guide](../tests/README.md) - Testing framework and examples
- [Deployment Guide](DEPLOYMENT.md) - Production deployment instructions
- [Migration Guide](MIGRATION.md) - Migrating from the original system
