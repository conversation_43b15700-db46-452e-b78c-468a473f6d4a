"""
Integration tests for enhanced agent based on browser-use patterns.
Tests agent execution, browser automation, LLM integration, and error scenarios.
"""

import asyncio
import pytest
import time
from typing import Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch

from src.agents.enhanced_agent import EnhancedAgent, AgentSettings
from src.browser.enhanced_browser import EnhancedBrowserSession
from src.llm.enhanced_llm import Enhanced<PERSON><PERSON>rovider, ChatInvokeCompletion, LLMMessage
from src.config.service import load_config
from src.utils.exceptions import <PERSON><PERSON>erError, LLMError, TimeoutError


class MockLLMProvider(EnhancedLLMProvider):
    """Mock LLM provider for testing."""
    
    def __init__(self, model: str = "test-model"):
        super().__init__(model)
        self.responses = []
        self.call_count = 0
    
    @property
    def provider(self) -> str:
        return "mock"
    
    async def ainvoke(self, messages, output_format=None, **kwargs):
        self.call_count += 1
        
        if self.responses:
            response_text = self.responses.pop(0)
        else:
            response_text = "Mock response"
        
        return ChatInvokeCompletion(
            completion=response_text,
            model=self.model,
            provider=self.provider,
            response_time=0.1
        )
    
    def add_response(self, response: str):
        """Add a mock response."""
        self.responses.append(response)


@pytest.fixture
async def mock_browser_session():
    """Create mock browser session."""
    session = AsyncMock(spec=EnhancedBrowserSession)
    session.session_id = "test-session-123"
    session.is_initialized = True
    session.get_current_page.return_value = AsyncMock()
    session.navigate_to_url.return_value = {"success": True}
    session.take_screenshot.return_value = "/tmp/test_screenshot.png"
    return session


@pytest.fixture
def mock_llm_provider():
    """Create mock LLM provider."""
    return MockLLMProvider()


@pytest.fixture
async def enhanced_agent(mock_browser_session, mock_llm_provider):
    """Create enhanced agent with mocks."""
    settings = AgentSettings(
        max_steps=5,
        step_timeout=10.0,
        max_failures=2
    )
    
    agent = EnhancedAgent(
        task="Test task",
        browser_session=mock_browser_session,
        llm=mock_llm_provider,
        settings=settings
    )
    
    # Mock initialization
    agent.is_initialized = True
    
    return agent


class TestEnhancedAgentExecution:
    """Test enhanced agent execution scenarios."""
    
    @pytest.mark.asyncio
    async def test_successful_task_execution(self, enhanced_agent, mock_llm_provider):
        """Test successful task execution."""
        # Setup mock responses
        mock_llm_provider.add_response('{"action": "navigate_to_url", "url": "https://example.com"}')
        mock_llm_provider.add_response('{"action": "done", "text": "Task completed", "success": true}')
        
        # Execute task
        result = await enhanced_agent.run("Navigate to example.com")
        
        # Verify results
        assert result["success"] is True
        assert result["steps_executed"] > 0
        assert result["agent_id"] == enhanced_agent.state.agent_id
        assert "execution_time" in result
    
    @pytest.mark.asyncio
    async def test_task_execution_with_failures(self, enhanced_agent, mock_llm_provider):
        """Test task execution with recoverable failures."""
        # Setup mock responses with failures
        mock_llm_provider.add_response('{"action": "invalid_action"}')  # Will fail
        mock_llm_provider.add_response('{"action": "navigate_to_url", "url": "https://example.com"}')
        mock_llm_provider.add_response('{"action": "done", "text": "Task completed", "success": true}')
        
        # Execute task
        result = await enhanced_agent.run("Navigate to example.com")
        
        # Verify recovery
        assert result["steps_executed"] > 1
        assert enhanced_agent.consecutive_failures == 0  # Should reset on success
    
    @pytest.mark.asyncio
    async def test_max_failures_reached(self, enhanced_agent, mock_llm_provider):
        """Test behavior when max failures is reached."""
        # Setup mock responses that will all fail
        for _ in range(5):
            mock_llm_provider.add_response('{"action": "invalid_action"}')
        
        # Execute task
        result = await enhanced_agent.run("Test task")
        
        # Verify failure handling
        assert result["success"] is False
        assert enhanced_agent.consecutive_failures >= enhanced_agent.settings.max_failures
    
    @pytest.mark.asyncio
    async def test_step_timeout_handling(self, enhanced_agent, mock_llm_provider):
        """Test step timeout handling."""
        # Mock a slow LLM response
        async def slow_response(*args, **kwargs):
            await asyncio.sleep(15)  # Longer than step timeout
            return ChatInvokeCompletion(completion="Slow response")
        
        mock_llm_provider.ainvoke = slow_response
        
        # Execute task with short timeout
        enhanced_agent.settings.step_timeout = 1.0
        result = await enhanced_agent.run("Test task")
        
        # Verify timeout handling
        assert result["success"] is False
        assert any("timed out" in str(r.get("error", "")) for r in result.get("results", []))


class TestBrowserIntegration:
    """Test browser integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_browser_navigation(self, enhanced_agent):
        """Test browser navigation functionality."""
        # Mock successful navigation
        enhanced_agent.browser_session.navigate_to_url.return_value = {
            "success": True,
            "url": "https://example.com",
            "title": "Example Domain"
        }
        
        # Test navigation
        result = await enhanced_agent.browser_session.navigate_to_url("https://example.com")
        
        assert result["success"] is True
        assert result["url"] == "https://example.com"
    
    @pytest.mark.asyncio
    async def test_browser_error_handling(self, enhanced_agent):
        """Test browser error handling."""
        # Mock browser error
        enhanced_agent.browser_session.navigate_to_url.side_effect = BrowserError(
            "Navigation failed",
            long_term_memory="Browser navigation error occurred",
            short_term_memory="Page failed to load"
        )
        
        # Test error handling
        with pytest.raises(BrowserError) as exc_info:
            await enhanced_agent.browser_session.navigate_to_url("https://invalid-url.com")
        
        assert "Navigation failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_screenshot_functionality(self, enhanced_agent):
        """Test screenshot functionality."""
        # Test screenshot
        screenshot_path = await enhanced_agent.browser_session.take_screenshot()
        
        assert screenshot_path == "/tmp/test_screenshot.png"
        enhanced_agent.browser_session.take_screenshot.assert_called_once()


class TestLLMIntegration:
    """Test LLM integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_llm_response_generation(self, mock_llm_provider):
        """Test LLM response generation."""
        mock_llm_provider.add_response("Test response from LLM")
        
        messages = [LLMMessage(role="user", content="Test message")]
        response = await mock_llm_provider.generate_response(messages)
        
        assert response.completion == "Test response from LLM"
        assert response.provider == "mock"
        assert response.model == "test-model"
        assert response.response_time is not None
    
    @pytest.mark.asyncio
    async def test_llm_timeout_handling(self, mock_llm_provider):
        """Test LLM timeout handling."""
        # Mock slow response
        async def slow_response(*args, **kwargs):
            await asyncio.sleep(5)
            return ChatInvokeCompletion(completion="Slow response")
        
        mock_llm_provider.ainvoke = slow_response
        
        # Test with short timeout
        messages = [LLMMessage(role="user", content="Test message")]
        
        with pytest.raises(TimeoutError):
            await mock_llm_provider.generate_response(messages, timeout=1.0)
    
    @pytest.mark.asyncio
    async def test_llm_error_handling(self, mock_llm_provider):
        """Test LLM error handling."""
        # Mock LLM error
        mock_llm_provider.ainvoke = AsyncMock(side_effect=Exception("LLM API error"))
        
        messages = [LLMMessage(role="user", content="Test message")]
        
        with pytest.raises(LLMError):
            await mock_llm_provider.generate_response(messages)
    
    def test_llm_statistics_tracking(self, mock_llm_provider):
        """Test LLM statistics tracking."""
        stats = mock_llm_provider.get_stats()
        
        assert "total_requests" in stats
        assert "successful_requests" in stats
        assert "failed_requests" in stats
        assert "provider" in stats
        assert "model" in stats
        assert stats["provider"] == "mock"
        assert stats["model"] == "test-model"


class TestErrorScenarios:
    """Test various error scenarios and recovery."""
    
    @pytest.mark.asyncio
    async def test_initialization_failure(self, mock_browser_session, mock_llm_provider):
        """Test agent initialization failure."""
        # Mock initialization failure
        mock_browser_session.initialize.return_value = False
        
        agent = EnhancedAgent(
            task="Test task",
            browser_session=mock_browser_session,
            llm=mock_llm_provider
        )
        
        # Test initialization
        success = await agent.initialize()
        assert success is False
    
    @pytest.mark.asyncio
    async def test_recovery_mechanisms(self, enhanced_agent):
        """Test recovery mechanisms."""
        # Test recovery attempt
        recovery_result = await enhanced_agent._attempt_recovery()
        
        # Should return recovery result structure
        assert isinstance(recovery_result, dict)
        assert "success" in recovery_result
    
    @pytest.mark.asyncio
    async def test_cleanup_on_error(self, enhanced_agent):
        """Test cleanup on error scenarios."""
        # Test cleanup
        await enhanced_agent.cleanup()
        
        # Verify cleanup was called
        enhanced_agent.browser_session.cleanup.assert_called_once()


class TestPerformanceBenchmarks:
    """Performance benchmarks for production readiness."""
    
    @pytest.mark.asyncio
    async def test_agent_initialization_performance(self, mock_browser_session, mock_llm_provider):
        """Test agent initialization performance."""
        start_time = time.time()
        
        agent = EnhancedAgent(
            task="Performance test",
            browser_session=mock_browser_session,
            llm=mock_llm_provider
        )
        
        await agent.initialize()
        
        initialization_time = time.time() - start_time
        
        # Should initialize quickly (under 1 second for mocked components)
        assert initialization_time < 1.0
    
    @pytest.mark.asyncio
    async def test_step_execution_performance(self, enhanced_agent, mock_llm_provider):
        """Test step execution performance."""
        mock_llm_provider.add_response('{"action": "done", "text": "Quick task", "success": true}')
        
        start_time = time.time()
        result = await enhanced_agent.run("Quick task")
        execution_time = time.time() - start_time
        
        # Should execute quickly with mocked components
        assert execution_time < 5.0
        assert result["success"] is True
    
    def test_memory_usage_tracking(self, enhanced_agent):
        """Test memory usage tracking."""
        stats = enhanced_agent.state.stats
        
        # Should have basic stats structure
        assert isinstance(stats, dict)
        assert "tasks_completed" in stats
        assert "tasks_failed" in stats
        assert "total_execution_time" in stats


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
