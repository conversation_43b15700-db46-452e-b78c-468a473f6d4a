#!/usr/bin/env python3
"""
Basic functionality test for the enhanced AI Sourcing Agent.
Tests core components without requiring external dependencies.
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all core modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test configuration
        from src.config.service import load_config
        print("✓ Configuration module imported")
        
        # Test logging
        from src.utils.logging_config import setup_logging
        print("✓ Logging module imported")
        
        # Test exceptions
        from src.utils.exceptions import BrowserError, LLMError, TimeoutError
        print("✓ Exception classes imported")
        
        # Test agent components
        from src.agents.enhanced_agent import EnhancedAgent, AgentSettings, AgentCurrentState
        print("✓ Agent classes imported")
        
        # Test browser components
        from src.browser.enhanced_browser import EnhancedBrowserSession
        print("✓ Browser classes imported")
        
        # Test LLM components
        from src.llm.enhanced_llm import EnhancedLLMProvider, ChatInvokeCompletion
        print("✓ LLM classes imported")
        
        # Test tools components
        from src.tools.enhanced_tools import EnhancedToolsManager, ActionResult
        print("✓ Tools classes imported")
        
        # Test DOM components
        from src.dom.enhanced_dom import EnhancedDOMService
        print("✓ DOM classes imported")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from src.config.service import Config, load_config
        
        # Test basic config creation
        config = Config()
        print("✓ Configuration object created")
        
        # Test attribute access
        browser_config = getattr(config, 'browser', {})
        print("✓ Configuration attributes accessible")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_logging():
    """Test logging setup."""
    print("\nTesting logging...")
    
    try:
        from src.utils.logging_config import setup_logging
        import logging
        
        # Setup logging
        setup_logging(log_level="INFO")
        
        # Test logger creation
        logger = logging.getLogger("test")
        logger.info("Test log message")
        print("✓ Logging setup successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_exceptions():
    """Test exception classes."""
    print("\nTesting exceptions...")
    
    try:
        from src.utils.exceptions import BrowserError, LLMError, TimeoutError
        
        # Test BrowserError
        browser_error = BrowserError(
            "Test browser error",
            long_term_memory="Long term context",
            short_term_memory="Short term context"
        )
        print("✓ BrowserError created successfully")
        
        # Test LLMError
        llm_error = LLMError("Test LLM error")
        print("✓ LLMError created successfully")
        
        # Test TimeoutError
        timeout_error = TimeoutError(
            operation="test_operation",
            timeout=30.0,
            message="Test timeout"
        )
        print("✓ TimeoutError created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Exception test failed: {e}")
        return False

def test_agent_classes():
    """Test agent class creation."""
    print("\nTesting agent classes...")
    
    try:
        from src.agents.enhanced_agent import AgentSettings, AgentCurrentState
        
        # Test AgentSettings
        settings = AgentSettings(
            max_steps=10,
            step_timeout=30.0,
            max_failures=3
        )
        print("✓ AgentSettings created successfully")
        
        # Test AgentCurrentState
        state = AgentCurrentState()
        print("✓ AgentCurrentState created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Agent classes test failed: {e}")
        return False

async def test_async_functionality():
    """Test basic async functionality."""
    print("\nTesting async functionality...")
    
    try:
        # Test basic async operation
        await asyncio.sleep(0.1)
        print("✓ Basic async operation successful")
        
        # Test async context manager pattern
        class MockAsyncContext:
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        async with MockAsyncContext():
            pass
        print("✓ Async context manager pattern works")
        
        return True
        
    except Exception as e:
        print(f"✗ Async functionality test failed: {e}")
        return False

def test_data_structures():
    """Test data structure creation."""
    print("\nTesting data structures...")
    
    try:
        from src.llm.enhanced_llm import ChatInvokeCompletion, ChatInvokeUsage
        from src.tools.enhanced_tools import ActionResult
        
        # Test ChatInvokeUsage
        usage = ChatInvokeUsage(
            prompt_tokens=100,
            completion_tokens=50,
            total_tokens=150
        )
        print("✓ ChatInvokeUsage created successfully")
        
        # Test ChatInvokeCompletion
        completion = ChatInvokeCompletion(
            completion="Test completion",
            usage=usage
        )
        print("✓ ChatInvokeCompletion created successfully")
        
        # Test ActionResult
        result = ActionResult(
            success=True,
            action_type="test_action",
            result="Test result",
            execution_time=0.1
        )
        print("✓ ActionResult created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Data structures test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("=" * 60)
    print("ENHANCED AI SOURCING AGENT - BASIC FUNCTIONALITY TEST")
    print("=" * 60)
    
    start_time = time.time()
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Logging Test", test_logging),
        ("Exception Test", test_exceptions),
        ("Agent Classes Test", test_agent_classes),
        ("Async Functionality Test", test_async_functionality),
        ("Data Structures Test", test_data_structures),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                failed += 1
                print(f"✗ {test_name} FAILED")
                
        except Exception as e:
            failed += 1
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    execution_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed / (passed + failed)) * 100:.1f}%")
    print(f"Execution Time: {execution_time:.2f} seconds")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! The enhanced system is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
