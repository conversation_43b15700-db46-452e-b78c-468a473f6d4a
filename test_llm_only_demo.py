#!/usr/bin/env python3
"""
LLM-Only Demo Test for Enhanced AI Sourcing Agent
Demonstrates LLM integration and AI sourcing capabilities without browser automation
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class LLMOnlyDemo:
    """LLM-focused demonstration of AI sourcing capabilities."""
    
    def __init__(self):
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging."""
        from src.utils.logging_config import setup_logging
        self.logger = setup_logging(log_level="INFO")
        self.logger.info("🧠 Starting LLM-Only AI Sourcing Demo")
    
    async def demo_gemini_sourcing_analysis(self):
        """Demonstrate Gemini API for sourcing analysis."""
        self.logger.info("🔍 Demo: Gemini-Powered Sourcing Analysis")
        
        try:
            from src.config.service import CONFIG
            
            if not (hasattr(CONFIG, 'GEMINI_API_KEY') and CONFIG.GEMINI_API_KEY):
                return {"error": "Gemini API key not configured"}
            
            import google.generativeai as genai
            
            genai.configure(api_key=CONFIG.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Test 1: Job Description Analysis
            self.logger.info("📋 Analyzing job descriptions for sourcing insights")
            
            job_description = """
            Senior Full-Stack Developer - Remote
            
            We're seeking a Senior Full-Stack Developer to join our growing team. The ideal candidate will have:
            
            Required Skills:
            - 5+ years of experience with React and Node.js
            - Strong proficiency in TypeScript and JavaScript
            - Experience with AWS cloud services (EC2, S3, Lambda)
            - Knowledge of microservices architecture
            - Proficiency with Docker and Kubernetes
            - Experience with PostgreSQL and Redis
            - Familiarity with CI/CD pipelines (Jenkins, GitHub Actions)
            
            Nice to Have:
            - Experience with GraphQL
            - Knowledge of machine learning concepts
            - Previous startup experience
            - Open source contributions
            
            Compensation: $130,000 - $170,000 + equity
            Location: Remote (US timezone)
            """
            
            analysis_prompt = f"""
            As an expert AI sourcing consultant, analyze this job description and provide comprehensive sourcing insights:
            
            {job_description}
            
            Please provide a detailed analysis in JSON format with the following structure:
            {{
                "primary_skills": ["skill1", "skill2", ...],
                "secondary_skills": ["skill1", "skill2", ...],
                "experience_level": "Senior/Mid/Junior",
                "salary_analysis": {{
                    "min": 130000,
                    "max": 170000,
                    "market_competitiveness": "competitive/above_market/below_market",
                    "analysis": "brief analysis"
                }},
                "sourcing_keywords": ["keyword1", "keyword2", ...],
                "alternative_titles": ["title1", "title2", ...],
                "sourcing_channels": ["channel1", "channel2", ...],
                "red_flags": ["flag1", "flag2", ...],
                "green_flags": ["flag1", "flag2", ...],
                "candidate_persona": "description of ideal candidate",
                "sourcing_strategy": "recommended approach"
            }}
            """
            
            response = model.generate_content(analysis_prompt)
            
            try:
                # Try to parse as JSON
                analysis_data = json.loads(response.text.strip())
                
                self.logger.info("✅ Job description analysis completed")
                self.logger.info(f"📊 Primary skills: {', '.join(analysis_data.get('primary_skills', [])[:5])}")
                self.logger.info(f"💰 Salary range: ${analysis_data.get('salary_analysis', {}).get('min', 0):,} - ${analysis_data.get('salary_analysis', {}).get('max', 0):,}")
                self.logger.info(f"🎯 Experience level: {analysis_data.get('experience_level', 'N/A')}")
                
                return {
                    "success": True,
                    "analysis": analysis_data,
                    "response_length": len(response.text)
                }
                
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                self.logger.info("✅ Job description analysis completed (text format)")
                return {
                    "success": True,
                    "analysis_text": response.text,
                    "response_length": len(response.text)
                }
            
        except Exception as e:
            self.logger.error(f"Gemini sourcing analysis failed: {e}")
            return {"error": str(e)}
    
    async def demo_candidate_matching(self):
        """Demonstrate candidate matching and scoring."""
        self.logger.info("🎯 Demo: AI-Powered Candidate Matching")
        
        try:
            from src.config.service import CONFIG
            
            if not (hasattr(CONFIG, 'GEMINI_API_KEY') and CONFIG.GEMINI_API_KEY):
                return {"error": "Gemini API key not configured"}
            
            import google.generativeai as genai
            
            genai.configure(api_key=CONFIG.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Sample candidate profiles
            candidates = [
                {
                    "name": "Alex Johnson",
                    "title": "Senior Software Engineer",
                    "experience": "6 years",
                    "skills": ["React", "Node.js", "TypeScript", "AWS", "Docker", "PostgreSQL"],
                    "previous_companies": ["TechCorp", "StartupXYZ"],
                    "education": "BS Computer Science",
                    "location": "San Francisco, CA"
                },
                {
                    "name": "Sarah Chen",
                    "title": "Full Stack Developer",
                    "experience": "4 years",
                    "skills": ["JavaScript", "React", "Python", "AWS", "Kubernetes", "GraphQL"],
                    "previous_companies": ["BigTech Inc", "Innovation Labs"],
                    "education": "MS Software Engineering",
                    "location": "Austin, TX"
                },
                {
                    "name": "Mike Rodriguez",
                    "title": "Backend Developer",
                    "experience": "7 years",
                    "skills": ["Node.js", "TypeScript", "Microservices", "Docker", "Redis", "Jenkins"],
                    "previous_companies": ["Enterprise Corp", "CloudTech"],
                    "education": "BS Computer Engineering",
                    "location": "Remote"
                }
            ]
            
            job_requirements = {
                "title": "Senior Full-Stack Developer",
                "required_skills": ["React", "Node.js", "TypeScript", "AWS", "Docker", "PostgreSQL"],
                "preferred_skills": ["Kubernetes", "GraphQL", "Microservices"],
                "min_experience": 5,
                "location": "Remote",
                "salary_range": [130000, 170000]
            }
            
            matching_prompt = f"""
            As an expert AI recruiter, evaluate these candidates against the job requirements and provide detailed matching analysis:
            
            Job Requirements:
            {json.dumps(job_requirements, indent=2)}
            
            Candidates:
            {json.dumps(candidates, indent=2)}
            
            For each candidate, provide a JSON analysis with:
            {{
                "candidate_name": "name",
                "overall_score": 85,
                "skill_match_score": 90,
                "experience_match_score": 80,
                "location_match_score": 100,
                "strengths": ["strength1", "strength2"],
                "gaps": ["gap1", "gap2"],
                "recommendation": "Strong match/Good match/Weak match",
                "interview_focus_areas": ["area1", "area2"],
                "salary_expectation": "estimated range"
            }}
            
            Provide the analysis for all candidates in a JSON array.
            """
            
            response = model.generate_content(matching_prompt)
            
            try:
                # Try to parse as JSON
                matching_data = json.loads(response.text.strip())
                
                self.logger.info("✅ Candidate matching analysis completed")
                
                for candidate in matching_data:
                    name = candidate.get('candidate_name', 'Unknown')
                    score = candidate.get('overall_score', 0)
                    recommendation = candidate.get('recommendation', 'N/A')
                    
                    self.logger.info(f"👤 {name}: {score}/100 - {recommendation}")
                
                # Find best match
                best_candidate = max(matching_data, key=lambda x: x.get('overall_score', 0))
                self.logger.info(f"🏆 Best match: {best_candidate.get('candidate_name')} ({best_candidate.get('overall_score')}/100)")
                
                return {
                    "success": True,
                    "matching_results": matching_data,
                    "best_candidate": best_candidate,
                    "candidates_evaluated": len(candidates)
                }
                
            except json.JSONDecodeError:
                self.logger.info("✅ Candidate matching completed (text format)")
                return {
                    "success": True,
                    "matching_text": response.text,
                    "candidates_evaluated": len(candidates)
                }
            
        except Exception as e:
            self.logger.error(f"Candidate matching demo failed: {e}")
            return {"error": str(e)}
    
    async def demo_market_intelligence(self):
        """Demonstrate market intelligence generation."""
        self.logger.info("📊 Demo: AI-Generated Market Intelligence")
        
        try:
            from src.config.service import CONFIG
            
            if not (hasattr(CONFIG, 'GEMINI_API_KEY') and CONFIG.GEMINI_API_KEY):
                return {"error": "Gemini API key not configured"}
            
            import google.generativeai as genai
            
            genai.configure(api_key=CONFIG.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Market intelligence prompt
            intelligence_prompt = """
            As a senior market research analyst specializing in tech talent, provide a comprehensive market intelligence report for Q4 2024 focusing on:
            
            1. Software Engineering Talent Market
            2. Salary Trends and Benchmarks
            3. In-Demand Skills and Technologies
            4. Remote Work Impact
            5. Sourcing Challenges and Opportunities
            
            Please structure your response as a professional market intelligence report with:
            - Executive Summary
            - Key Market Trends
            - Salary Benchmarks (by role and experience level)
            - Top 10 In-Demand Skills
            - Geographic Insights
            - Sourcing Recommendations
            - Future Outlook
            
            Focus on actionable insights for talent acquisition teams.
            """
            
            response = model.generate_content(intelligence_prompt)
            
            # Extract key metrics from the response
            report_sections = response.text.split('\n\n')
            
            self.logger.info("✅ Market intelligence report generated")
            self.logger.info(f"📄 Report length: {len(response.text)} characters")
            self.logger.info(f"📋 Report sections: {len(report_sections)}")
            
            # Generate summary insights
            summary_prompt = f"""
            Based on this market intelligence report, extract the top 5 most actionable insights for talent acquisition teams:
            
            {response.text[:2000]}...
            
            Provide exactly 5 bullet points, each starting with an action verb (e.g., "Focus on...", "Prioritize...", "Consider...").
            """
            
            summary_response = model.generate_content(summary_prompt)
            
            insights = [
                line.strip() 
                for line in summary_response.text.split('\n') 
                if line.strip() and (line.strip().startswith('•') or line.strip().startswith('-') or line.strip().startswith('*'))
            ][:5]
            
            self.logger.info("🎯 Top actionable insights:")
            for i, insight in enumerate(insights, 1):
                clean_insight = insight.lstrip('•-* ').strip()
                self.logger.info(f"  {i}. {clean_insight}")
            
            return {
                "success": True,
                "full_report": response.text,
                "report_length": len(response.text),
                "sections_count": len(report_sections),
                "actionable_insights": insights,
                "summary": summary_response.text
            }
            
        except Exception as e:
            self.logger.error(f"Market intelligence demo failed: {e}")
            return {"error": str(e)}
    
    async def demo_sourcing_strategy(self):
        """Demonstrate AI-powered sourcing strategy generation."""
        self.logger.info("🎯 Demo: AI-Powered Sourcing Strategy")
        
        try:
            from src.config.service import CONFIG
            
            if not (hasattr(CONFIG, 'GEMINI_API_KEY') and CONFIG.GEMINI_API_KEY):
                return {"error": "Gemini API key not configured"}
            
            import google.generativeai as genai
            
            genai.configure(api_key=CONFIG.GEMINI_API_KEY)
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Sourcing challenge scenario
            scenario = {
                "role": "Senior Machine Learning Engineer",
                "company": "AI Startup (Series B)",
                "location": "San Francisco / Remote",
                "salary": "$180k - $220k + equity",
                "urgency": "High (need to fill in 30 days)",
                "challenges": [
                    "Highly competitive market",
                    "Specific ML expertise required (NLP, Computer Vision)",
                    "Startup environment may not appeal to all candidates",
                    "Limited employer brand recognition"
                ],
                "requirements": [
                    "PhD or MS in ML/AI/CS",
                    "5+ years ML experience",
                    "Experience with PyTorch/TensorFlow",
                    "Production ML systems experience",
                    "Strong Python and cloud skills"
                ]
            }
            
            strategy_prompt = f"""
            As an expert executive search consultant specializing in AI/ML talent, develop a comprehensive sourcing strategy for this challenging role:
            
            {json.dumps(scenario, indent=2)}
            
            Provide a detailed sourcing strategy including:
            
            1. SOURCING CHANNELS (ranked by effectiveness)
            2. SEARCH STRINGS (Boolean search queries for different platforms)
            3. OUTREACH STRATEGY (messaging templates and sequences)
            4. COMPETITIVE INTELLIGENCE (companies to target/avoid)
            5. TIMELINE AND MILESTONES (30-day execution plan)
            6. SUCCESS METRICS (KPIs to track)
            7. RISK MITIGATION (addressing the challenges)
            
            Make it actionable and specific to this role and market conditions.
            """
            
            response = model.generate_content(strategy_prompt)
            
            # Extract search strings from the response
            search_strings_prompt = f"""
            From this sourcing strategy, extract and format the Boolean search strings for LinkedIn, GitHub, and Google:
            
            {response.text}
            
            Provide them in this format:
            LinkedIn: (search string)
            GitHub: (search string)
            Google: (search string)
            """
            
            search_response = model.generate_content(search_strings_prompt)
            
            self.logger.info("✅ Comprehensive sourcing strategy generated")
            self.logger.info(f"📄 Strategy length: {len(response.text)} characters")
            
            # Log key search strings
            search_lines = [line.strip() for line in search_response.text.split('\n') if ':' in line][:3]
            self.logger.info("🔍 Generated search strings:")
            for search_line in search_lines:
                self.logger.info(f"  {search_line}")
            
            return {
                "success": True,
                "full_strategy": response.text,
                "search_strings": search_response.text,
                "strategy_length": len(response.text),
                "scenario": scenario
            }
            
        except Exception as e:
            self.logger.error(f"Sourcing strategy demo failed: {e}")
            return {"error": str(e)}
    
    async def run_complete_demo(self):
        """Run the complete LLM-only demonstration."""
        self.logger.info("🚀 Starting Complete LLM-Only Demo")
        
        start_time = time.time()
        
        demos = [
            ("Gemini Sourcing Analysis", self.demo_gemini_sourcing_analysis),
            ("Candidate Matching", self.demo_candidate_matching),
            ("Market Intelligence", self.demo_market_intelligence),
            ("Sourcing Strategy", self.demo_sourcing_strategy),
        ]
        
        results = {}
        successful_demos = 0
        
        for demo_name, demo_func in demos:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"🧠 RUNNING: {demo_name}")
            self.logger.info(f"{'='*60}")
            
            try:
                result = await demo_func()
                results[demo_name] = result
                
                if result.get("success", False):
                    successful_demos += 1
                    self.logger.info(f"✅ {demo_name} COMPLETED SUCCESSFULLY")
                else:
                    self.logger.error(f"❌ {demo_name} FAILED: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                results[demo_name] = {"error": str(e)}
                self.logger.error(f"❌ {demo_name} FAILED with exception: {e}")
        
        execution_time = time.time() - start_time
        
        # Generate final summary
        summary = {
            "execution_time": execution_time,
            "successful_demos": successful_demos,
            "total_demos": len(demos),
            "success_rate": (successful_demos / len(demos)) * 100,
            "results": results,
            "ai_capabilities_demonstrated": [
                "✅ Job description analysis and skill extraction",
                "✅ Candidate matching and scoring algorithms",
                "✅ Market intelligence report generation",
                "✅ Strategic sourcing plan development",
                "✅ Boolean search string generation",
                "✅ Competitive analysis and insights",
                "✅ Real-time AI-powered decision making"
            ]
        }
        
        self.logger.info(f"\n{'='*70}")
        self.logger.info("🧠 LLM-ONLY DEMO SUMMARY")
        self.logger.info(f"{'='*70}")
        self.logger.info(f"Execution Time: {execution_time:.2f} seconds")
        self.logger.info(f"Success Rate: {summary['success_rate']:.1f}%")
        self.logger.info(f"Successful Demos: {successful_demos}/{len(demos)}")
        
        for capability in summary["ai_capabilities_demonstrated"]:
            self.logger.info(capability)
        
        if successful_demos == len(demos):
            self.logger.info("\n🎉 ALL LLM DEMOS COMPLETED SUCCESSFULLY!")
            self.logger.info("🧠 AI-powered sourcing capabilities fully operational!")
        elif successful_demos >= len(demos) * 0.75:
            self.logger.info(f"\n✅ {successful_demos}/{len(demos)} demos completed successfully")
            self.logger.info("🔧 Strong AI capabilities demonstrated")
        else:
            self.logger.warning("\n⚠️ Some critical demos failed. Check API configuration.")
        
        # Save results
        with open("llm_demo_results.json", "w") as f:
            json.dump(summary, f, indent=2, default=str)
        
        return summary

async def main():
    """Run the LLM-only demonstration."""
    demo = LLMOnlyDemo()
    results = await demo.run_complete_demo()
    return 0 if results["successful_demos"] > 0 else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
