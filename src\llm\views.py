"""
Enhanced LLM views and response models based on browser-use patterns.
"""

from typing import Generic, TypeVar, Union, Optional
from pydantic import BaseModel, Field

T = TypeVar('T', bound=Union[BaseModel, str])


class ChatInvokeUsage(BaseModel):
    """
    Usage information for a chat model invocation.
    Based on browser-use patterns for comprehensive cost tracking.
    """
    
    prompt_tokens: int = Field(..., description="The number of tokens in the prompt")
    prompt_cached_tokens: Optional[int] = Field(None, description="The number of cached tokens")
    prompt_cache_creation_tokens: Optional[int] = Field(None, description="Anthropic only: tokens used to create cache")
    prompt_image_tokens: Optional[int] = Field(None, description="Google only: tokens in images")
    completion_tokens: int = Field(..., description="The number of tokens in the completion")
    total_tokens: int = Field(..., description="The total number of tokens")
    
    # Cost tracking
    prompt_cost: Optional[float] = Field(None, description="Cost of prompt tokens")
    completion_cost: Optional[float] = Field(None, description="Cost of completion tokens")
    total_cost: Optional[float] = Field(None, description="Total cost of the request")
    
    # Provider-specific metadata
    model_name: Optional[str] = Field(None, description="Name of the model used")
    provider: Optional[str] = Field(None, description="LLM provider name")
    
    def calculate_total_cost(self, prompt_rate: float, completion_rate: float) -> float:
        """Calculate total cost based on token rates."""
        prompt_cost = (self.prompt_tokens - (self.prompt_cached_tokens or 0)) * prompt_rate / 1000
        completion_cost = self.completion_tokens * completion_rate / 1000
        
        self.prompt_cost = prompt_cost
        self.completion_cost = completion_cost
        self.total_cost = prompt_cost + completion_cost
        
        return self.total_cost


class ChatInvokeCompletion(BaseModel, Generic[T]):
    """
    Response from a chat model invocation.
    Enhanced with browser-use patterns for structured outputs and thinking.
    """
    
    completion: T = Field(..., description="The completion response")
    
    # Thinking capabilities (for models that support it)
    thinking: Optional[str] = Field(None, description="Model's thinking process")
    redacted_thinking: Optional[str] = Field(None, description="Redacted thinking for privacy")
    
    # Usage and cost information
    usage: Optional[ChatInvokeUsage] = Field(None, description="Token usage information")
    
    # Response metadata
    model_name: Optional[str] = Field(None, description="Name of the model used")
    provider: Optional[str] = Field(None, description="LLM provider name")
    response_time: Optional[float] = Field(None, description="Response time in seconds")
    
    # Request metadata
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    timestamp: Optional[str] = Field(None, description="Response timestamp")
    
    @property
    def content(self) -> str:
        """Get content as string for backward compatibility."""
        if isinstance(self.completion, str):
            return self.completion
        elif hasattr(self.completion, 'content'):
            return self.completion.content
        else:
            return str(self.completion)


class LLMError(BaseModel):
    """Structured error information from LLM providers."""
    
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Provider-specific error code")
    retryable: bool = Field(False, description="Whether the error is retryable")
    retry_after: Optional[float] = Field(None, description="Seconds to wait before retry")
    
    # Context information
    model_name: Optional[str] = Field(None, description="Model that caused the error")
    provider: Optional[str] = Field(None, description="Provider that caused the error")
    request_id: Optional[str] = Field(None, description="Request ID for debugging")


class ModelCapabilities(BaseModel):
    """Model capabilities and limitations."""
    
    supports_vision: bool = Field(False, description="Supports image inputs")
    supports_function_calling: bool = Field(False, description="Supports function/tool calling")
    supports_structured_output: bool = Field(False, description="Supports structured JSON output")
    supports_streaming: bool = Field(False, description="Supports streaming responses")
    supports_caching: bool = Field(False, description="Supports prompt caching")
    supports_thinking: bool = Field(False, description="Supports thinking/reasoning")
    
    max_tokens: Optional[int] = Field(None, description="Maximum tokens per request")
    max_context_length: Optional[int] = Field(None, description="Maximum context length")
    max_output_tokens: Optional[int] = Field(None, description="Maximum output tokens")
    
    # Cost information (per 1K tokens)
    input_cost_per_1k: Optional[float] = Field(None, description="Cost per 1K input tokens")
    output_cost_per_1k: Optional[float] = Field(None, description="Cost per 1K output tokens")
    cached_input_cost_per_1k: Optional[float] = Field(None, description="Cost per 1K cached input tokens")


class ProviderConfig(BaseModel):
    """Configuration for LLM providers."""
    
    provider_name: str = Field(..., description="Name of the provider")
    model_name: str = Field(..., description="Name of the model")
    api_key: Optional[str] = Field(None, description="API key for the provider")
    base_url: Optional[str] = Field(None, description="Base URL for API calls")
    
    # Generation parameters
    temperature: Optional[float] = Field(0.7, description="Temperature for generation")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    top_p: Optional[float] = Field(None, description="Top-p sampling parameter")
    top_k: Optional[int] = Field(None, description="Top-k sampling parameter")
    frequency_penalty: Optional[float] = Field(None, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(None, description="Presence penalty")
    
    # Timeout and retry settings
    timeout: float = Field(30.0, description="Request timeout in seconds")
    max_retries: int = Field(3, description="Maximum number of retries")
    retry_delay: float = Field(1.0, description="Delay between retries in seconds")
    
    # Provider-specific settings
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="Provider-specific parameters")


class TokenCostTracker(BaseModel):
    """Tracks token usage and costs across multiple requests."""
    
    total_requests: int = Field(0, description="Total number of requests")
    total_prompt_tokens: int = Field(0, description="Total prompt tokens used")
    total_completion_tokens: int = Field(0, description="Total completion tokens used")
    total_cached_tokens: int = Field(0, description="Total cached tokens used")
    total_cost: float = Field(0.0, description="Total cost incurred")
    
    # Per-model tracking
    model_usage: Dict[str, Dict[str, Union[int, float]]] = Field(
        default_factory=dict, 
        description="Usage statistics per model"
    )
    
    def add_usage(self, usage: ChatInvokeUsage, model_name: str):
        """Add usage statistics from a request."""
        self.total_requests += 1
        self.total_prompt_tokens += usage.prompt_tokens
        self.total_completion_tokens += usage.completion_tokens
        self.total_cached_tokens += usage.prompt_cached_tokens or 0
        self.total_cost += usage.total_cost or 0.0
        
        # Track per-model usage
        if model_name not in self.model_usage:
            self.model_usage[model_name] = {
                "requests": 0,
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "cached_tokens": 0,
                "cost": 0.0
            }
        
        model_stats = self.model_usage[model_name]
        model_stats["requests"] += 1
        model_stats["prompt_tokens"] += usage.prompt_tokens
        model_stats["completion_tokens"] += usage.completion_tokens
        model_stats["cached_tokens"] += usage.prompt_cached_tokens or 0
        model_stats["cost"] += usage.total_cost or 0.0
    
    def get_average_cost_per_request(self) -> float:
        """Get average cost per request."""
        return self.total_cost / self.total_requests if self.total_requests > 0 else 0.0
    
    def get_model_stats(self, model_name: str) -> Optional[Dict[str, Union[int, float]]]:
        """Get statistics for a specific model."""
        return self.model_usage.get(model_name)
    
    def reset(self):
        """Reset all statistics."""
        self.total_requests = 0
        self.total_prompt_tokens = 0
        self.total_completion_tokens = 0
        self.total_cached_tokens = 0
        self.total_cost = 0.0
        self.model_usage.clear()


class LLMResponse(BaseModel):
    """Unified response format for all LLM providers."""
    
    content: str = Field(..., description="Response content")
    usage: Optional[ChatInvokeUsage] = Field(None, description="Token usage information")
    model_name: str = Field(..., description="Name of the model used")
    provider: str = Field(..., description="LLM provider name")
    
    # Optional fields
    thinking: Optional[str] = Field(None, description="Model's thinking process")
    function_calls: Optional[List[Dict[str, Any]]] = Field(None, description="Function calls made")
    structured_output: Optional[BaseModel] = Field(None, description="Structured output if requested")
    
    # Metadata
    request_id: Optional[str] = Field(None, description="Request identifier")
    response_time: Optional[float] = Field(None, description="Response time in seconds")
    timestamp: Optional[str] = Field(None, description="Response timestamp")
    
    @classmethod
    def from_completion(cls, completion: ChatInvokeCompletion, provider: str, model_name: str) -> "LLMResponse":
        """Create LLMResponse from ChatInvokeCompletion."""
        return cls(
            content=completion.content,
            usage=completion.usage,
            model_name=model_name,
            provider=provider,
            thinking=completion.thinking,
            request_id=completion.request_id,
            response_time=completion.response_time,
            timestamp=completion.timestamp
        )
