# Enhanced AI Sourcing Agent - Test Suite

This directory contains comprehensive tests for the enhanced AI Sourcing Agent system, built with browser-use patterns for production reliability.

## Test Structure

```
tests/
├── conftest.py              # Pytest configuration and fixtures
├── run_tests.py             # Test runner script
├── README.md               # This file
├── integration/            # Integration tests
│   └── test_enhanced_system.py
└── unit/                   # Unit tests (to be added)
    ├── test_agents/
    ├── test_browser/
    ├── test_dom/
    ├── test_llm/
    ├── test_tools/
    ├── test_config/
    └── test_observability/
```

## Test Categories

### Integration Tests
- **Location**: `tests/integration/`
- **Purpose**: Test complete system workflows and component interactions
- **Features Tested**:
  - System initialization and configuration
  - Browser automation and DOM interaction
  - LLM provider integration
  - Tools and action execution
  - Event-driven architecture
  - Observability and telemetry
  - End-to-end workflows

### Unit Tests
- **Location**: `tests/unit/`
- **Purpose**: Test individual components in isolation
- **Coverage**: All major system components

## Running Tests

### Using the Test Runner

The `run_tests.py` script provides a convenient way to run different types of tests:

```bash
# Run all tests
python tests/run_tests.py all --verbose

# Run only integration tests
python tests/run_tests.py integration --verbose

# Run unit tests with coverage
python tests/run_tests.py unit --coverage

# Validate system components
python tests/run_tests.py validate

# Run specific test file
python tests/run_tests.py specific --test-path tests/integration/test_enhanced_system.py

# Check system requirements
python tests/run_tests.py all --check-requirements
```

### Using Pytest Directly

```bash
# Run all tests
pytest tests/ -v --asyncio-mode=auto

# Run integration tests only
pytest tests/integration/ -v --asyncio-mode=auto

# Run with coverage
pytest tests/ --cov=src --cov-report=html --cov-report=term

# Run specific test class
pytest tests/integration/test_enhanced_system.py::TestEnhancedSystemIntegration -v

# Run tests with specific markers
pytest -m "not browser" tests/  # Skip browser tests
pytest -m "integration" tests/  # Run only integration tests
```

## Test Configuration

### Environment Variables

The following environment variables are used for testing:

```bash
# Logging
AI_SOURCING_LOGGING_LEVEL=DEBUG

# Telemetry
ANONYMIZED_TELEMETRY=false

# Browser settings
AI_SOURCING_HEADLESS=true
AI_SOURCING_BROWSER_TYPE=chromium

# API keys (for testing)
GOOGLE_API_KEY=test-google-key
GEMINI_API_KEY=test-gemini-key
OPENAI_API_KEY=test-openai-key
ANTHROPIC_API_KEY=test-anthropic-key

# Configuration
AI_SOURCING_CONFIG_DIR=/tmp/test-config
```

### Test Fixtures

Key fixtures available in `conftest.py`:

- `temp_dir`: Temporary directory for test files
- `test_config_dir`: Test configuration directory
- `mock_api_keys`: Mock API keys for testing
- `clean_observability`: Clean observability data between tests
- `event_loop`: Async event loop for tests

## Test Features

### Browser Automation Testing
- Headless browser testing for CI/CD compatibility
- DOM extraction and element interaction validation
- Navigation and page loading verification
- Screenshot and visual testing capabilities

### LLM Integration Testing
- Mock LLM providers for consistent testing
- Message handling and conversation management
- Cost tracking and usage monitoring
- Structured output validation

### Tools and Actions Testing
- Action registration and execution
- Parameter validation and error handling
- Browser interaction through tools
- Custom action development testing

### Event System Testing
- Event emission and handling
- Asynchronous event processing
- Event bus functionality
- Cross-component communication

### Observability Testing
- Metrics collection and aggregation
- Distributed tracing validation
- Telemetry event recording
- Performance monitoring

### Configuration Testing
- Environment variable handling
- Profile management
- Configuration validation
- Migration and compatibility

## Test Data and Mocking

### Mock Components
- **Mock LLM Provider**: Provides consistent responses for testing
- **Mock API Keys**: Prevents real API calls during testing
- **Test HTML Pages**: Simple HTML for DOM and interaction testing

### Test Data
- Configuration profiles for different test scenarios
- Sample HTML content for browser testing
- Mock responses for LLM interactions
- Test metrics and telemetry events

## Continuous Integration

### GitHub Actions (Example)

```yaml
name: Test Enhanced AI Sourcing Agent

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Install Playwright browsers
      run: playwright install chromium
    
    - name: Run tests
      run: python tests/run_tests.py all --coverage --check-requirements
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
```

## Test Markers

Available pytest markers:

- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.slow`: Slow-running tests
- `@pytest.mark.browser`: Tests requiring browser
- `@pytest.mark.asyncio`: Async tests

## Debugging Tests

### Running Tests in Debug Mode

```bash
# Enable debug logging
export AI_SOURCING_LOGGING_LEVEL=DEBUG

# Run specific test with verbose output
pytest tests/integration/test_enhanced_system.py::TestEnhancedSystemIntegration::test_browser_navigation -v -s

# Run with pdb debugger
pytest --pdb tests/integration/test_enhanced_system.py
```

### Test Debugging Tips

1. **Use `--capture=no` or `-s`** to see print statements
2. **Set breakpoints** with `import pdb; pdb.set_trace()`
3. **Check logs** in the test output for detailed information
4. **Use temporary directories** for inspecting test artifacts
5. **Enable browser head mode** for visual debugging (set `AI_SOURCING_HEADLESS=false`)

## Contributing Tests

### Adding New Tests

1. **Unit Tests**: Add to appropriate subdirectory in `tests/unit/`
2. **Integration Tests**: Add to `tests/integration/`
3. **Use Fixtures**: Leverage existing fixtures in `conftest.py`
4. **Follow Patterns**: Use existing tests as templates
5. **Add Markers**: Use appropriate pytest markers

### Test Guidelines

1. **Isolation**: Tests should be independent and not affect each other
2. **Cleanup**: Use fixtures for proper setup and teardown
3. **Mocking**: Mock external dependencies and API calls
4. **Assertions**: Use clear, descriptive assertions
5. **Documentation**: Add docstrings explaining test purpose

### Test Coverage Goals

- **Unit Tests**: 90%+ coverage for individual components
- **Integration Tests**: Cover all major workflows
- **Error Handling**: Test error conditions and recovery
- **Performance**: Include performance and load testing
- **Compatibility**: Test across different configurations

## Troubleshooting

### Common Issues

1. **Browser Not Found**: Install Playwright browsers with `playwright install`
2. **Async Test Failures**: Ensure `--asyncio-mode=auto` is used
3. **Import Errors**: Check that `src/` is in Python path
4. **Configuration Issues**: Verify test environment variables
5. **Resource Leaks**: Check for proper cleanup in fixtures

### Getting Help

- Check test logs for detailed error information
- Run system validation: `python tests/run_tests.py validate`
- Verify requirements: `python tests/run_tests.py all --check-requirements`
- Review test configuration in `conftest.py`
- Check browser automation setup with Playwright
