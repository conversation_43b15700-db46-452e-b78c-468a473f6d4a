# Enhanced AI Sourcing Agent Usage Guide

## Quick Start

### Basic Usage

```python
import asyncio
from src.agents.enhanced_agent import Enhanced<PERSON><PERSON>, AgentSettings
from src.browser.enhanced_browser import EnhancedBrowserSession
from src.llm.enhanced_llm import Enhanced<PERSON><PERSON>anager
from src.config.service import load_config

async def main():
    # Load configuration
    config = load_config()
    
    # Create browser session
    browser_session = EnhancedBrowserSession(config.browser)
    await browser_session.initialize()
    
    # Create LLM manager
    llm_manager = EnhancedLLMManager(config.llm)
    
    # Create agent with settings
    settings = AgentSettings(
        max_steps=50,
        step_timeout=30.0,
        max_failures=3
    )
    
    agent = EnhancedAgent(
        task="Navigate to Google and search for 'AI automation'",
        browser_session=browser_session,
        llm=llm_manager,
        settings=settings
    )
    
    # Initialize and run
    await agent.initialize()
    result = await agent.run()
    
    print(f"Task completed: {result['success']}")
    print(f"Steps executed: {result['steps_executed']}")
    
    # Cleanup
    await agent.cleanup()
    await browser_session.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration

### 1. File-based Configuration

Create `config.json`:

```json
{
  "browser": {
    "type": "chromium",
    "headless": false,
    "timeout": 30.0,
    "viewport": {
      "width": 1920,
      "height": 1080
    },
    "user_data_dir": null,
    "args": ["--no-sandbox", "--disable-dev-shm-usage"]
  },
  "llm": {
    "provider": "gemini",
    "model": "gemini-2.0-flash-exp",
    "api_key": "your-api-key-here",
    "timeout": 30.0,
    "max_retries": 3,
    "cost_tracking": true
  },
  "agent": {
    "max_steps": 50,
    "step_timeout": 30.0,
    "max_failures": 3,
    "enable_recovery": true,
    "screenshot_on_error": true
  },
  "logging": {
    "level": "INFO",
    "file_logging": true,
    "log_dir": "logs",
    "max_file_size": "10MB",
    "backup_count": 5
  }
}
```

### 2. Environment Variables

```bash
# Browser configuration
export BROWSER_TYPE=chromium
export BROWSER_HEADLESS=false
export BROWSER_TIMEOUT=30.0

# LLM configuration
export LLM_PROVIDER=gemini
export LLM_MODEL=gemini-2.0-flash-exp
export LLM_API_KEY=your-api-key-here
export LLM_TIMEOUT=30.0

# Agent configuration
export AGENT_MAX_STEPS=50
export AGENT_STEP_TIMEOUT=30.0
export AGENT_MAX_FAILURES=3

# Logging configuration
export LOG_LEVEL=INFO
export LOG_FILE_ENABLED=true
```

## Advanced Usage

### Custom LLM Provider

```python
from src.llm.enhanced_llm import EnhancedLLMProvider, ChatInvokeCompletion

class CustomLLMProvider(EnhancedLLMProvider):
    @property
    def provider(self) -> str:
        return "custom"
    
    async def ainvoke(self, messages, output_format=None, **kwargs):
        # Your custom LLM implementation
        response_text = await self.call_custom_api(messages)
        
        return ChatInvokeCompletion(
            completion=response_text,
            model=self.model,
            provider=self.provider,
            response_time=0.5
        )

# Use custom provider
custom_llm = CustomLLMProvider(model="custom-model")
agent = EnhancedAgent(task="...", llm=custom_llm, ...)
```

### Event Handling

```python
from src.events import default_event_bus

# Subscribe to events
async def on_step_completed(event_data):
    print(f"Step completed: {event_data}")

async def on_browser_error(event_data):
    print(f"Browser error: {event_data}")

# Register event handlers
default_event_bus.subscribe('step_completed', on_step_completed)
default_event_bus.subscribe('browser_error', on_browser_error)

# Run agent with event monitoring
result = await agent.run()
```

### Custom Tools

```python
from src.tools.enhanced_tools import EnhancedToolsManager, ActionResult
from pydantic import BaseModel, Field

class CustomAction(BaseModel):
    """Custom action parameters."""
    parameter1: str = Field(description="First parameter")
    parameter2: int = Field(description="Second parameter")

class CustomToolsManager(EnhancedToolsManager):
    async def custom_action(self, parameter1: str, parameter2: int) -> ActionResult:
        """Execute custom action."""
        try:
            # Your custom action implementation
            result = f"Executed with {parameter1} and {parameter2}"
            
            return ActionResult(
                success=True,
                action_type="custom_action",
                result=result,
                execution_time=0.1
            )
        except Exception as e:
            return ActionResult(
                success=False,
                action_type="custom_action",
                error=str(e),
                execution_time=0.1
            )

# Use custom tools
custom_tools = CustomToolsManager(browser_session)
agent = EnhancedAgent(task="...", tools=custom_tools, ...)
```

### Error Handling and Recovery

```python
from src.utils.exceptions import BrowserError, LLMError, TimeoutError

async def robust_agent_execution():
    try:
        result = await agent.run()
        return result
    
    except BrowserError as e:
        print(f"Browser error: {e}")
        print(f"Long-term memory: {e.long_term_memory}")
        print(f"Short-term memory: {e.short_term_memory}")
        
        # Attempt recovery
        recovery_result = await agent._attempt_recovery()
        if recovery_result["success"]:
            # Retry after recovery
            return await agent.run()
    
    except LLMError as e:
        print(f"LLM error: {e}")
        # Handle LLM-specific errors
    
    except TimeoutError as e:
        print(f"Timeout error: {e}")
        print(f"Operation: {e.operation}")
        print(f"Timeout: {e.timeout}")
    
    except Exception as e:
        print(f"Unexpected error: {e}")
        # Handle unexpected errors
    
    return {"success": False, "error": str(e)}
```

### Performance Monitoring

```python
import time
from src.utils.logging_config import setup_logging

# Setup performance logging
setup_logging(log_level="INFO", info_log_file="performance.log")

async def monitor_performance():
    start_time = time.time()
    
    # Execute agent
    result = await agent.run()
    
    execution_time = time.time() - start_time
    
    # Get statistics
    agent_stats = agent.state.stats
    browser_stats = browser_session.stats
    llm_stats = llm_manager.get_stats()
    
    print(f"Execution time: {execution_time:.2f}s")
    print(f"Steps executed: {agent_stats['tasks_completed']}")
    print(f"Browser navigations: {browser_stats['navigation_count']}")
    print(f"LLM requests: {llm_stats['total_requests']}")
    print(f"Total cost: ${llm_stats['total_cost']:.4f}")
```

## Testing

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock

# Run all tests
python tests/run_integration_tests.py --suite all

# Run specific test suite
python tests/run_integration_tests.py --suite browser
python tests/run_integration_tests.py --suite llm
python tests/run_integration_tests.py --suite agent

# Run with verbose output
python tests/run_integration_tests.py --suite all --verbose

# Run performance tests
python tests/run_integration_tests.py --suite performance
```

### Writing Custom Tests

```python
import pytest
from tests.conftest import MockFactory

@pytest.mark.asyncio
async def test_custom_functionality():
    # Create mocks
    mock_factory = MockFactory()
    browser_session = mock_factory.create_mock_browser_session()
    llm_provider = mock_factory.create_mock_llm_provider()
    
    # Test your functionality
    agent = EnhancedAgent(
        task="Test task",
        browser_session=browser_session,
        llm=llm_provider
    )
    
    result = await agent.run()
    assert result["success"] is True
```

## Best Practices

### 1. Configuration Management
- Use environment variables for sensitive data (API keys)
- Keep configuration files in version control (without secrets)
- Use different configurations for development/staging/production

### 2. Error Handling
- Always wrap agent execution in try-catch blocks
- Implement proper cleanup in finally blocks
- Use structured error handling with specific exception types

### 3. Performance Optimization
- Monitor execution times and resource usage
- Use headless mode for production environments
- Implement proper timeout values for your use case

### 4. Security
- Never commit API keys to version control
- Use secure credential storage in production
- Implement proper access controls and logging

### 5. Monitoring and Observability
- Enable comprehensive logging in production
- Monitor key metrics (success rate, execution time, costs)
- Set up alerts for error conditions
- Use event-driven monitoring for real-time insights

## Troubleshooting

### Common Issues

1. **Browser fails to start**
   - Check browser installation
   - Verify user permissions
   - Check available memory and disk space

2. **LLM requests timeout**
   - Increase timeout values
   - Check network connectivity
   - Verify API key and quotas

3. **Agent gets stuck in loops**
   - Review max_steps configuration
   - Check step timeout values
   - Enable debug logging to trace execution

4. **High memory usage**
   - Ensure proper cleanup after execution
   - Monitor browser session lifecycle
   - Check for memory leaks in custom code

### Debug Mode

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed agent logging
agent_settings = AgentSettings(
    max_steps=10,
    step_timeout=60.0,
    debug_mode=True,
    screenshot_on_error=True
)
```

This guide provides comprehensive information for using the Enhanced AI Sourcing Agent effectively in various scenarios.
