# Enhanced AI Sourcing Agent - Configuration Guide

This guide covers all configuration options for the enhanced AI Sourcing Agent system.

## 📋 Table of Contents

- [Configuration Overview](#configuration-overview)
- [Environment Variables](#environment-variables)
- [Configuration Files](#configuration-files)
- [Browser Profiles](#browser-profiles)
- [LLM Configuration](#llm-configuration)
- [Agent Configuration](#agent-configuration)
- [Advanced Settings](#advanced-settings)

## 🔧 Configuration Overview

The enhanced system uses a hierarchical configuration approach:

1. **Environment Variables** (highest priority)
2. **Configuration Files** (JSON format)
3. **Default Values** (fallback)

### Configuration Loading Order

```
Environment Variables → JSON Config Files → Default Values
```

## 🌍 Environment Variables

### Core Settings

```bash
# Logging
AI_SOURCING_LOGGING_LEVEL=INFO          # DEBUG, INFO, WARNING, ERROR
AI_SOURCING_LOG_FILE=app.log            # Log file path

# Browser Settings
AI_SOURCING_HEADLESS=false              # Run browser in headless mode
AI_SOURCING_BROWSER_TYPE=chromium       # Browser type (chromium, firefox, webkit)
AI_SOURCING_WINDOW_WIDTH=1280           # Browser window width
AI_SOURCING_WINDOW_HEIGHT=720           # Browser window height
AI_SOURCING_TIMEOUT=30.0                # Default timeout in seconds

# Stealth and Security
AI_SOURCING_ENABLE_STEALTH=true         # Enable stealth mode
AI_SOURCING_USER_AGENT="custom-agent"   # Custom user agent

# Proxy Settings
AI_SOURCING_PROXY_SERVER=http://proxy:8080
AI_SOURCING_PROXY_USERNAME=user
AI_SOURCING_PROXY_PASSWORD=pass

# Performance
AI_SOURCING_MAX_CONCURRENT_TABS=5       # Maximum concurrent tabs
AI_SOURCING_PAGE_LOAD_TIMEOUT=30.0      # Page load timeout
```

### LLM API Keys

```bash
# Google Gemini
GOOGLE_API_KEY=your_google_api_key
GEMINI_API_KEY=your_gemini_api_key      # Alias for GOOGLE_API_KEY

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key

# LLM Settings
AI_SOURCING_LLM_PROVIDER=gemini         # Default LLM provider
AI_SOURCING_LLM_MODEL=gemini-2.0-flash-exp
AI_SOURCING_LLM_TEMPERATURE=0.7
AI_SOURCING_LLM_MAX_TOKENS=8192
```

### Agent Behavior

```bash
# Agent Settings
AI_SOURCING_MAX_STEPS=50                # Maximum agent steps
AI_SOURCING_STEP_TIMEOUT=60.0           # Step timeout in seconds
AI_SOURCING_MAX_RETRIES=3               # Maximum retry attempts
AI_SOURCING_ENABLE_SCREENSHOTS=false    # Enable screenshot capture

# Observability
ANONYMIZED_TELEMETRY=true               # Enable anonymized telemetry
AI_SOURCING_ENABLE_TELEMETRY=true       # Enable telemetry collection
AI_SOURCING_DEBUG=false                 # Enable debug mode
```

### Configuration Directory

```bash
# Configuration
AI_SOURCING_CONFIG_DIR=/path/to/config  # Configuration directory
AI_SOURCING_CONFIG_FILE=config.json     # Configuration file name
```

## 📄 Configuration Files

### Main Configuration File

Create `config.json` in your configuration directory:

```json
{
  "browser_profiles": {
    "default": {
      "name": "default",
      "headless": false,
      "window_width": 1280,
      "window_height": 720,
      "timeout": 30.0,
      "enable_stealth": true,
      "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "proxy": null
    },
    "production": {
      "name": "production",
      "headless": true,
      "window_width": 1920,
      "window_height": 1080,
      "timeout": 60.0,
      "enable_stealth": true,
      "proxy": {
        "server": "http://proxy.example.com:8080",
        "username": "user",
        "password": "pass"
      }
    }
  },
  "llm_configs": {
    "default": {
      "name": "default",
      "provider": "gemini",
      "model": "gemini-2.0-flash-exp",
      "temperature": 0.7,
      "max_tokens": 8192,
      "enable_cost_tracking": true
    },
    "openai": {
      "name": "openai",
      "provider": "openai",
      "model": "gpt-4",
      "temperature": 0.7,
      "max_tokens": 4096,
      "enable_cost_tracking": true
    }
  },
  "agent_configs": {
    "default": {
      "name": "default",
      "max_steps": 50,
      "step_timeout": 60.0,
      "enable_screenshots": false,
      "max_retries": 3,
      "enable_telemetry": true
    },
    "production": {
      "name": "production",
      "max_steps": 100,
      "step_timeout": 120.0,
      "enable_screenshots": true,
      "max_retries": 5,
      "enable_telemetry": true
    }
  }
}
```

## 🌐 Browser Profiles

### Basic Browser Profile

```python
from src.config.service import BrowserProfile

profile = BrowserProfile(
    name="basic",
    headless=True,
    window_width=1280,
    window_height=720,
    timeout=30.0
)
```

### Advanced Browser Profile

```python
from src.config.service import BrowserProfile, ProxySettings

profile = BrowserProfile(
    name="advanced",
    headless=False,
    window_width=1920,
    window_height=1080,
    timeout=60.0,
    enable_stealth=True,
    user_agent="Custom User Agent",
    proxy=ProxySettings(
        server="http://proxy.example.com:8080",
        username="proxy_user",
        password="proxy_pass"
    ),
    extra_args=[
        "--disable-blink-features=AutomationControlled",
        "--disable-dev-shm-usage",
        "--no-sandbox"
    ]
)
```

### Browser Profile Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `name` | str | "default" | Profile name |
| `headless` | bool | True | Run in headless mode |
| `window_width` | int | 1280 | Browser window width |
| `window_height` | int | 720 | Browser window height |
| `timeout` | float | 30.0 | Default timeout |
| `enable_stealth` | bool | True | Enable stealth mode |
| `user_agent` | str | None | Custom user agent |
| `proxy` | ProxySettings | None | Proxy configuration |
| `extra_args` | List[str] | [] | Additional browser arguments |

## 🤖 LLM Configuration

### Gemini Configuration

```python
from src.config.service import LLMConfig

gemini_config = LLMConfig(
    name="gemini-pro",
    provider="gemini",
    model="gemini-2.0-flash-exp",
    api_key="your-api-key",
    temperature=0.7,
    max_tokens=8192,
    enable_cost_tracking=True
)
```

### OpenAI Configuration

```python
openai_config = LLMConfig(
    name="gpt4",
    provider="openai",
    model="gpt-4",
    api_key="your-openai-key",
    temperature=0.7,
    max_tokens=4096,
    enable_cost_tracking=True
)
```

### LLM Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `name` | str | "default" | Configuration name |
| `provider` | str | "gemini" | LLM provider |
| `model` | str | "gemini-2.0-flash-exp" | Model name |
| `api_key` | str | Required | API key |
| `temperature` | float | 0.7 | Response randomness |
| `max_tokens` | int | 8192 | Maximum tokens |
| `enable_cost_tracking` | bool | True | Track usage costs |

## 🎯 Agent Configuration

### Basic Agent Configuration

```python
from src.config.service import AgentConfig

agent_config = AgentConfig(
    name="basic-agent",
    max_steps=30,
    step_timeout=45.0,
    enable_screenshots=False,
    max_retries=2,
    enable_telemetry=True
)
```

### Production Agent Configuration

```python
production_config = AgentConfig(
    name="production-agent",
    max_steps=100,
    step_timeout=120.0,
    enable_screenshots=True,
    max_retries=5,
    enable_telemetry=True,
    screenshot_quality=90,
    enable_debug_logs=False
)
```

### Agent Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `name` | str | "default" | Configuration name |
| `max_steps` | int | 50 | Maximum execution steps |
| `step_timeout` | float | 60.0 | Step timeout in seconds |
| `enable_screenshots` | bool | False | Capture screenshots |
| `max_retries` | int | 3 | Maximum retry attempts |
| `enable_telemetry` | bool | True | Enable telemetry |
| `screenshot_quality` | int | 80 | Screenshot quality (1-100) |
| `enable_debug_logs` | bool | False | Enable debug logging |

## ⚙️ Advanced Settings

### Environment Configuration

```python
from src.config.service import EnvironmentConfig

env_config = EnvironmentConfig(
    environment="production",  # development, staging, production
    debug_mode=False,
    log_level="INFO",
    enable_telemetry=True,
    config_validation=True
)
```

### Proxy Configuration

```python
from src.config.service import ProxySettings

proxy = ProxySettings(
    server="http://proxy.example.com:8080",
    username="proxy_user",
    password="proxy_pass",
    bypass_list=["localhost", "127.0.0.1", "*.local"]
)
```

### Performance Tuning

```python
performance_config = {
    "max_concurrent_tabs": 5,
    "page_load_timeout": 30.0,
    "dom_extraction_timeout": 10.0,
    "element_interaction_timeout": 5.0,
    "screenshot_timeout": 10.0,
    "memory_limit_mb": 2048,
    "cpu_limit_percent": 80
}
```

## 🔧 Configuration Loading

### Using Configuration Service

```python
from src.config import config_service

# Load all configurations
config_data = config_service.load_config()

# Get specific configurations
browser_profile = config_service.get_browser_profile("production")
llm_config = config_service.get_llm_config("gemini-pro")
agent_config = config_service.get_agent_config("production")

# Validate configuration
is_valid = config_service.validate_configuration()
```

### Environment-Specific Configurations

```python
import os

# Set environment
os.environ["AI_SOURCING_ENVIRONMENT"] = "production"

# Load environment-specific config
config_service = ConfigurationService()
config_data = config_service.load_config()
```

## 🔍 Configuration Validation

### Validation Rules

The system validates configurations for:
- Required fields presence
- Data type correctness
- Value range validation
- API key format validation
- URL format validation
- File path existence

### Custom Validation

```python
from src.config.service import ConfigurationService

class CustomConfigService(ConfigurationService):
    def validate_custom_rules(self, config_data: Dict[str, Any]) -> bool:
        # Add custom validation logic
        return True

config_service = CustomConfigService()
```

## 🚀 Quick Configuration Examples

### Development Setup

```bash
# .env file for development
AI_SOURCING_LOGGING_LEVEL=DEBUG
AI_SOURCING_HEADLESS=false
AI_SOURCING_ENABLE_SCREENSHOTS=true
AI_SOURCING_DEBUG=true
GOOGLE_API_KEY=your_dev_api_key
```

### Production Setup

```bash
# .env file for production
AI_SOURCING_LOGGING_LEVEL=INFO
AI_SOURCING_HEADLESS=true
AI_SOURCING_ENABLE_SCREENSHOTS=false
AI_SOURCING_MAX_STEPS=100
AI_SOURCING_STEP_TIMEOUT=120.0
GOOGLE_API_KEY=your_prod_api_key
```

### Testing Setup

```bash
# .env file for testing
AI_SOURCING_LOGGING_LEVEL=DEBUG
AI_SOURCING_HEADLESS=true
AI_SOURCING_ENABLE_TELEMETRY=false
GOOGLE_API_KEY=test-api-key
```

This configuration guide provides comprehensive coverage of all configuration options available in the enhanced AI Sourcing Agent system. Use these examples and options to customize the system for your specific needs.
